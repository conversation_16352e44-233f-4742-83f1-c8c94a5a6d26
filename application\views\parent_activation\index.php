<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('SchoolAdmin_menu');?>">User Management</a></li>
    <li>Provision Parent Credentials</li>
</ul>

<div class="col-md-12 col_new_padding ">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <h3 class="card-title panel_title_new_style_staff">
                    <a class="back_anchor" href="<?php echo site_url('SchoolAdmin_menu') ?>" class="control-primary">
                        <span class="fa fa-arrow-left"></span>
                    </a>
                    Provision Parent Credentials
                </h3>
            </div>
        </div>
        <div class="card-body pt-1">
            <?php if ($smsConfigured == 0 || $emailConfigured == 0) { ?>
                <div class="col-md-12">
                    <p class="text-danger" style="font-size: 14px;"> 
                        <?php if($smsConfigured == 0 && $emailConfigured == 0) { ?>
                            <strong>Note: </strong>
                            <strong>SMS and Email are not configured. To use either of these features, please contact the School Administrator or IT Team for further assistance.</strong>
                        <?php } else if($emailConfigured == 0) { ?>
                            <strong class="text-success">Currently, only SMS is in use.</strong>
                            <br>
                            <strong>Note: </strong>
                            <strong>Please contact the Administrator or IT Team to enable the Email feature if needed.</strong>
                        <?php } else if($smsConfigured == 0) { ?>
                            <strong class="text-success">Currently, only Email is in use.</strong>
                            <br>
                            <strong>Note: </strong>
                            <strong>Please contact the Administrator or IT Team to enable the SMS feature if needed.</strong>
                        <?php } ?>
                    </p>
                </div>
            <?php } else if($smsConfigured == 1 && $emailConfigured == 1) { ?>
                <div class="col-md-12">
                    <p class="text-success" style="font-size: 14px;"> 
                        <strong>Note: </strong>
                        <strong>SMS and Email Templates are configured.</strong>
                    </p>
                </div>
            <?php }?>
            <form id="selectStd" enctype="multipart/form-data" id="user_credentials_students" class="form-horizontal">
                <div class="form-group col-md-2">
                    <label for="class" class="control-label">Class <font style="color: red;">*</font></label>
                    <div>
                        <select id="classSectionId" name="classSectionId" required class="form-control input-md">
                            <?php
                        $selected = '';
                        if($classSectionId == -1)
                            $selected = ' selected';
                            echo '<option value="0">Select Class</option>';
                            echo '<option value="-1" '.$selected.'>All classes</option>';  //-1 for all classes
                        foreach ($classSectionList as $cl) {
                            $option = '<option value="' . $cl->id.'_'.$cl->class_id . '"';
                            if($cl->id.'_'.$cl->class_id==$classSectionId){
                                $option.='selected';
                            }
                            $option .= '>' . $cl->class_name . $cl->section_name.'</option>';
                            echo $option;
                        }
                        ?>
                        </select>
                    </div>
                </div>
                <div class="form-group col-md-3">
                    <label for="class" class="control-label">Student Name</label>
                    <input type="text" class="form-control" name="student_name" id="student_name"
                        value="<?php echo $student_name ?>" placeholder="Student Name">
                </div>
                <div class="form-group col-md-3 pt-2">
                    <input type="button" value="Get" onclick="get_credentials_student_data()"class="btn btn-primary mt-4" id="getReportButton"/>
                </div>
            </form>
        </div>
        <div class="card-body pt-0">
            <div class="col-12 text-center loading-icon" style="display: none;">
                <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
            </div>
            <div class="text-center">
                <div style="display: none;" class="progress" id="progress">
                    <div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated"
                        role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%">
                    </div>
                </div>
            </div>
            <div class="stdudentData tableFixHead">
                <div class="no-data-display">Select Filter To Get Student List</div>
            </div>
        </div>
    </div>
</div>

<style>
.tableFixHead {
    overflow-y: auto;
    /* make the table scrollable if height is more than 200 px  */
    height: 35rem;
    /* gives an initial height of 200px to the table */
}

.tableFixHead thead th {
    position: sticky;
    /* make the table heads sticky */
    top: -2px;
    /* table head will be placed from the top of the table and sticks to it */
}

table {
    border-collapse: collapse;
    /* make the table borders collapse to each other */
    width: 100%;
}

th,
td {
    padding: 8px 16px;
    border: 1px solid #ccc;
}

th {
    background: #eee;
}
</style>


<style type="text/css">
.btn-danger {
    border-radius: 0.2rem;
}

.medium {
    width: 40%;
    margin: auto;
}

.statusColumn button{
    cursor: default !important;
}
#student_provision td, 
#student_provision th {
    white-space: normal !important;
    word-break: break-word;
}
</style>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript">
var total_students = 0;
var completed = 0;

function get_credentials_student_data() {
    $('.stdudentData').html('');
    total_students = 0;
    completed = 0;
    var classSectionId = $('#classSectionId').val();
    if (classSectionId == 0) {
        alert('Select Class');
        return false;
    }
    var student_name = $('#student_name').val();
    $('#getReportButton').prop('disabled', true).val('Please wait...');
    $.ajax({
        url: '<?php echo site_url('parent_activation/get_credentials_student_ids_list_by_filter'); ?>',
        type: 'post',
        data: {
            'classSectionId': classSectionId,
            'student_name': student_name
        },
        success: function(data) {
            $('.loading-icon').hide();
            var data = JSON.parse(data);
            if (data.length <= 0) {
                $('.stdudentData').html('<div class="no-data-display">No Data Found</div>');
                $('#getReportButton').prop('disabled', false).val('Get');
                return false;
            }
            var students = data;
            studentIds = students;
            total_students = parseInt(100 * (studentIds.length - 2)) + parseInt(studentIds[studentIds
                .length - 1].length);
            var progress = document.getElementById('progress-ind');
            progress.style.width = (completed / total_students) * 100 + '%';
            $("#progress").show();
            report_index(0);
        }
    });
}

function report_index(index) {
    if (index < studentIds.length) {
        get_fee_report(index);
    } else {
        $("#progress").hide();
        $('#getReportButton').prop('disabled', false).val('Get');
    }
}

function get_fee_report(index) {
    // console.log(index);
    var student_ids = studentIds[index];
    $.ajax({
        url: '<?php echo site_url('parent_activation/get_credentials_student_data'); ?>',
        type: 'post',
        data: {
            'student_ids': student_ids
        },
        success: function(data) {
            var jsonData = JSON.parse(data);
            var stdData = jsonData;
            if (index == 0) {
                constructFeeHeader(stdData);
            }
            completed += Object.keys(stdData).length;
            var progress = document.getElementById('progress-ind');
            progress.style.width = (completed / total_students) * 100 + '%';

            prepare_student_table(stdData, index);

        }
    });
}

function constructFeeHeader(stdData) {
    let smsConfigured = <?php echo $smsConfigured; ?>;
    let emailConfigured = <?php echo $emailConfigured; ?>;
    var html = `<table class="table table-bordered" id="student_provision">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Student Name</th>
                    <th>Relation</th>
                    <th>Parent Name</th>
                    <th>Mobile Number</th>
                    <th>Email</th>
                    <th>Admission Status</th>
                    <th>
                        <select class="form-control" onchange="send_method_type(this.value)" name="send_method_type" style="width: 70px;">`;
                        if(smsConfigured == 0 && emailConfigured == 0) {
                            html += '<option value="sms" disabled selected>SMS</option><option value="email" disabled>Email</option>';
                        } else if(smsConfigured == 0) {
                            html += '<option value="email">Email</option>';
                        } else if(emailConfigured == 0) {
                            html += '<option value="sms">SMS</option>';
                        } else {
                            html += '<option value="sms">SMS</option><option value="email">Email</option>';
                        }
                    html += `</select>
                    </th>
                    <th>Status</th>`;
                    if(smsConfigured == 0 && emailConfigured == 0) {
                        html += `
                            <th>
                                <input type="button" id="sendButton" disabled class="btn btn-primary" value="Send">
                                <br><br>Send <input type="checkbox" name="selectAll" disabled value="send" onclick="check_all(this, value)" id="sendAll" class="check">
                            </th>
                            <th>
                                <input type="button" id="re-sendButton" disabled class="btn btn-primary" value="Re-Send">
                                <br><br>Re-send <input type="checkbox" name="selectAll" disabled value="re_send" onclick="check_all(this, value)" id="re_sendAll" class="check">
                            </th>
                        `;
                    } else {
                        html += `<th>
                                    <input type="button" onclick="send_provision_credentials()" id="sendButton" disabled="true"  class="btn btn-primary" value="Send">
                                    <br><br>Send <input type="checkbox" name="selectAll" value="send" onclick="check_all(this, value)" id="sendAll" class="check">
                                </th>
                                <th>
                                    <input type="button" onclick="re_send_provision_credentials()" id="re-sendButton" disabled="true"  class="btn btn-primary" value="Re-Send">
                                    <br><br>Re-send <input type="checkbox" name="selectAll" value="re_send" onclick="check_all(this, value)" id="re_sendAll" class="check">
                                </th>`;
                    }
                    html += `<th>
                        <input type="button" onclick="deactivate_provision_credentials()" id="deactivateButton" disabled="true"  class="btn btn-primary" value="Deactivate">
                        <br><br>Deactivate <input type="checkbox" name="selectAll" value="deactivate" onclick="check_all(this, value)" id="deactivateAll" class="check">
                    </th>
                    <th>
                        <input type="button" onclick="reset_selected_passwords()" id="resetButton" disabled class="btn btn-primary" value="Reset Passwords">
                        <br><br>Reset <input type="checkbox" name="selectAll" value="reset" onclick="check_all(this, value)" id="resetAll" class="check">
                    </th>
                </tr>
            </thead>
        </table>`;

    $('.stdudentData').html(html);
}

var new_index = 0;

function prepare_student_table(stdData, index) {
    var srNo = index * new_index;
    var m = 0;
    var html = '';
    let smsConfigured = <?php echo $smsConfigured; ?>;
    let emailConfigured = <?php echo $emailConfigured; ?>;
    html += '<tbody>';
    for (var k in stdData) {
        var activeDisabled = 0;
        let hasMobileNumber = false;
        let hasEmail = false;
        if (stdData[k].Active == 1) {
            activeDisabled = 1;
        }
        let mobileNumber = 'N/A';
        if (stdData[k].mobile != null && stdData[k].mobile.trim() != '') {
            mobileNumber = stdData[k].mobile;
            hasMobileNumber = true;
        }
        let email = 'N/A';
        if (stdData[k].email != null && stdData[k].email.trim() != '') {
            email = stdData[k].email;
            hasEmail = true;
        }

        html += `<tr id="row_${stdData[k].pid}">`;
        html += '<td>' + (m + 1 + srNo) + '</td>';
        html += '<td>' + stdData[k].studentName + '</td>';
        html += '<td>' + stdData[k].relation_type + '</td>';
        html += '<td>' + stdData[k].parentName + '</td>';
        html += '<td>' + mobileNumber + '</td>';
        html += '<td id="parent-email' + stdData[k].pid + '" >' + email + '</td>';
        html += '<td>' + stdData[k].admissionStatus + '</td>';

        if (stdData[k].oldUid != null && stdData[k].userId != stdData[k].oldUid) {
            html += '<td>';
            for (var j = 0; j < stdData[k].siblings.length; j++) {
                html += `<p>${stdData[k].siblings[j].stdName}  (${stdData[k].siblings[j].aSection})</p>`;
            }
            html += '</td>'
        } else {
            html +='<td><select class="form-control method_type_dropdown" name="select_send_options" id="method_type_indiv_' + stdData[k].pid + '">';
                if(smsConfigured == 0 && emailConfigured == 0) {
                    html += '<option value="sms" disabled selected>SMS</option><option value="email" disabled>Email</option>';
                } else if(smsConfigured == 0) {
                    html += '<option value="email">Email</option>';
                } else if(emailConfigured == 0) {
                    html += '<option value="sms">SMS</option>';
                } else {
                    html += '<option value="sms">SMS</option><option value="email">Email</option>';
                }
            html += '</select></td>';
        }

        //Display select box for choosing SMS or Email
        if (stdData[k].oldUid != null && stdData[k].userId != stdData[k].oldUid) {
            loggedStatus = 'Sibling Connected';
        } else {
            if (stdData[k].loggedin_atleast_once == 1) {
                loggedStatus = '<button type="button" class="btn btn-info btn-xs">Logged-in</button>';
            } else if (stdData[k].Active == 1) {
                loggedStatus = '<button type="button" class="btn btn-warning btn-xs">Activated</button> ';
            } else {
                loggedStatus = '<button type="button" class="btn btn-danger btn-xs">Not activated</button>';
            }
        }
        html += '<td class="statusColumn">' + loggedStatus + '</td>';

        //Display Send button
        if (stdData[k].oldUid != null && stdData[k].userId != stdData[k].oldUid) {
            html += '<td>Sibling connected</td>';
        } else {
            if (activeDisabled) {
                html += (stdData[k].loggedin_atleast_once == 1) ? '<td><font color="green">Logged-in</font></td>' :
                    '<td><font color="orange">Activated</font></td>';
            } else {
                if(smsConfigured == 0 && emailConfigured == 0) {
                    html += '<td>-</td>';
                } else {
                    html +=
                        `<td><input type="checkbox" onclick="check_smsIndividual()" name="send_credentials" value="${stdData[k].pid}" class="sendCheck"></td>`;
                }
            }
        }

        //Display Resend button
        if (stdData[k].oldUid != null && stdData[k].userId != stdData[k].oldUid) {
            html += '<td>Sibling connected</td>';
        } else {
            if (activeDisabled) {
                if(smsConfigured == 0 && emailConfigured == 0) {
                    html += '<td>-</td>';
                } else {
                    html += `<td><input type="checkbox" onclick="check_reSMSIndividual()" name="re_send_credentials" value="${stdData[k].pid}" class="re_sendCheck"></td>`;
                }
            } else {
                html += '<td>Not Activated</td>';
            }
        }

        //Display Deactivate button
        if (stdData[k].oldUid != null && stdData[k].userId != stdData[k].oldUid) {
            html += `<td>Sibling connected</td>`;
        } else if(activeDisabled){
            html +=
                `<td><input type="checkbox" onclick="check_deactivateIndividual()" name="deactivate_credentials" value="${stdData[k].userId}" class="deactivate_sendCheck"></td>`;
        } else {
            html += '<td>Not Activated</td>';
        }

        //Display Reset Password button
        if (stdData[k].oldUid != null && stdData[k].userId != stdData[k].oldUid) {
            html += `<td>Sibling connected</td>`;
        } else {
            if (activeDisabled) {
                html += `<td><input type="checkbox" class="resetCheck" onclick="check_resetIndividual()" value="${stdData[k].userId}" data-username="${stdData[k].username}"></td>`;
            } else {
                html += '<td>Not Activated</td>';
            }
        }
        html += '</tr>';

        m++;
    }
    $('#student_provision').append(html);
    index++;
    new_index = m;
    report_index(index);
}

function reset_selected_passwords() {
    const selectedCheckboxes = $('.resetCheck:checked');

    if (selectedCheckboxes.length === 0) {
        Swal.fire({
            icon: 'warning',
            title: 'No Selection',
            text: 'Please select at least one user.',
            confirmButtonColor: '#ffc107'
        });
        return;
    }

    // Gather userIds and usernames
    const users = Array.from(selectedCheckboxes).map(cb => ({
        userId: $(cb).val(),
        username: $(cb).data('username'),
        checkbox: cb
    }));

    const usernamesList = users.map(u => `<li>${u.username}</li>`).join('');
    Swal.fire({
        title: 'Reset to Default Password?',
        html: `
            <b>The following usernames will be reset:</b>
            <div style="max-height: 200px; overflow-y: auto; text-align: left; margin-top: 10px; border: 1px solid #ddd; padding: 10px; border-radius: 4px;">
                <ul style="margin: 0; padding-left: 20px;">
                    ${usernamesList}
                </ul>
            </div>
            <br><b>Password:</b> welcome123
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, Reset All',
        cancelButtonText: 'No',
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#dc3545',
        customClass: {
            popup: 'medium'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            users.forEach(({ userId, username, checkbox }) => {
                $.ajax({
                    url: '<?php echo site_url("parent_activation/reset_default_password_user_id"); ?>',
                    type: 'POST',
                    data: { userId: userId },
                    success: function (data) {
                        if (data) {
                            $(checkbox).prop('disabled', true).prop('checked', false);
                            $(checkbox).closest('td').append('<font style="margin-left: 5%" color="green">Reset</font>');
                        } else {
                            $(checkbox).closest('td').append('<font style="margin-left: 5%" color="red">Failed</font>');
                        }
                    },
                    error: function () {
                        $(checkbox).closest('td').append('<font style="margin-left: 5%" class="red">Error</font>');
                    }
                });
            });

            Swal.fire({
                icon: 'success',
                title: 'Reset Started',
                text: 'Password reset is being processed for selected users.',
                confirmButtonColor: '#28a745'
            });
        }
    });
}

function check_all(check, value) {
    if (value == 'send') {
        isCheckedBySend(check)
    }
    if (value == 're_send') {
        isCheckedByReSend(check)
    }

    if (value == 'deactivate') {
        isCheckedByDeactivate(check)
    }

    if (value == 'reset') {
        isCheckedByReset(check)
    }
}

function isCheckedByReset(check) {
    if ($(check).is(':checked')) {
        $('.resetCheck').prop('checked', true);
        $('#resetButton').prop('disabled', false);

        // Optionally disable others
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop({
            "checked": false,
            "disabled": true
        });
    } else {
        $('.resetCheck').prop('checked', false);
        $('#resetButton').prop('disabled', true);

        // Enable others
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled', false);
    }
}

function isCheckedBySend(check) {
    if ($(check).is(':checked')) {
        $('.sendCheck').prop('checked', true);
        $('#sendButton').prop('disabled', false);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll, #resetAll, .resetCheck').prop({
            "disabled": "true",
            "checked": ""
        });
    } else {
        $('.sendCheck').prop('checked', false);
        $('#sendButton').prop('disabled', true);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll, #resetAll, .resetCheck').prop('disabled', false);
    }
}

function isCheckedByReSend(check) {
    if ($(check).is(':checked')) {
        $('.re_sendCheck').prop('checked', true);
        $('#re-sendButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll, #resetAll, .resetCheck').prop({
            "disabled": "true",
            "checked": ""
        });
    } else {
        $('.re_sendCheck').prop('checked', false);
        $('#re-sendButton').prop('disabled', true);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll, #resetAll, .resetCheck').prop('disabled', false);
    }
}

function isCheckedByDeactivate(check) {
    if ($(check).is(':checked')) {
        $('.deactivate_sendCheck').prop('checked', true);
        $('#deactivateButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll, #resetAll, .resetCheck').prop({
            "disabled": "true",
            "checked": ""
        });
    } else {
        $('.deactivate_sendCheck').prop('checked', false);
        $('#deactivateButton').prop('disabled', true);
        <?php if($smsConfigured == 0 && $emailConfigured == 0) { ?>
            $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll').prop('disabled', true);
        <?php } else { ?>
            $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll, #resetAll, .resetCheck').prop('disabled', false);
        <?php }?>
    }
}

function send_method_type(value) {
    $('.method_type_dropdown').each(function () {
        $(this).val(value); // this sets the selected option correctly
    });
}

function check_smsIndividual() {
    if ($("input[class='sendCheck']:checked").length > 0) {
        $('#sendButton').prop('disabled', false);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll, #resetAll, .resetCheck').prop({
            "disabled": "true",
            "checked": ""
        });
    } else {
        $('.sendCheck').prop('checked', false);
        $('#sendButton').prop('disabled', true);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll, #resetAll, .resetCheck').prop('disabled', false);
    }
}

function check_reSMSIndividual() {
    if ($("input[class='re_sendCheck']:checked").length > 0) {
        $('#re-sendButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll, #resetAll, .resetCheck').prop({
            "disabled": "true",
            "checked": ""
        });
    } else {
        $('.re_sendCheck').prop('checked', false);
        $('#re-sendButton').prop('disabled', true);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll, #resetAll, .resetCheck').prop('disabled', false);
    }
}

function check_deactivateIndividual() {
    if ($("input[class='deactivate_sendCheck']:checked").length > 0) {
        $('#deactivateButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll, #resetAll, .resetCheck').prop({
            "disabled": "true",
            "checked": ""
        });
    } else {
        $('.deactivate_sendCheck').prop('checked', false);
        $('#deactivateButton').prop('disabled', true);
        <?php if($smsConfigured == 0 && $emailConfigured == 0) { ?>
            $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll, #resetAll, .resetCheck').prop('disabled', true);
        <?php } else { ?>
            $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll, #resetAll, .resetCheck').prop('disabled', false);
        <?php }?>
    }
}

function check_resetIndividual() {
    if ($("input.resetCheck:checked").length > 0) {
        $('#resetButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop({
            "disabled": true,
            "checked": false
        });
    } else {
        $('#resetButton').prop('disabled', true);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled', false);
    }
}


function send_provision_credentials() {
    var pids = [];
    var smsType = [];
    $('.sendCheck:checked').each(function() {
        var methodType = $('#method_type_indiv_' + $(this).val()).val();
        if (methodType == 'sms') {
            pids.push($(this).val() + '_' + $('#method_type_indiv_' + $(this).val()).val());
        }
        if (methodType == 'email' && $('#parent-email' + $(this).val()).html() != '') {
            pids.push($(this).val() + '_' + $('#method_type_indiv_' + $(this).val()).val());
        }
    });
    if (pids.length <= 0) {
        return false;
    }
    $("#credit-err").hide();
    $("#confirmBtn").attr('disabled', true);
    $('#summary').modal('show');
    sendProvisionLink(pids, 'activation');
    $("#process").val('activation');
    $("#smsType").val('send');
}

function re_send_provision_credentials() {
    var pids = [];
    var smsType = [];
    $('.re_sendCheck:checked').each(function() {
        var methodType = $('#method_type_indiv_' + $(this).val()).val();
        if (methodType == 'sms') {
            pids.push($(this).val() + '_' + $('#method_type_indiv_' + $(this).val()).val());
        }
        if (methodType == 'email' && $('#parent-email' + $(this).val()).html() != '') {
            pids.push($(this).val() + '_' + $('#method_type_indiv_' + $(this).val()).val());
        }
    });
    if (pids.length <= 0) {
        return false;
    }
    $("#credit-err").hide();
    $("#confirmBtn").attr('disabled', true);
    $('#summary').modal('show');
    sendProvisionLink(pids, 'activation');
    $("#process").val('activation');
    $("#smsType").val('send');
}

function sendProvisionLink(pids, provision = 'activation', alreadyProv = 0) {
    $('#modal-loader').show();
    if (pids.length > 0) {
        $('#dynamic-content').html('');
        $("#warnMsg").html('');
        $.ajax({
                url: '<?php echo site_url('parent_activation/getPreview_credentials'); ?>',
                type: 'POST',
                data: {
                    'pids': pids,
                    'process': provision
                },
                dataType: 'html'
            })
            .done(function(data) {
                var data = $.parseJSON(data);
                if(data.config){
                    $('#modal-loader').hide();
                    $("#credit-err").hide();
                    $('#dynamic-content').html(`<div class="no-data-display">${data.config}</div>`);
                    return;
                }
                var previewData = data.preview;
                var credits_available = data.credits_available;
                var html = '';
                let hasData = false;
                $('#dynamic-content').html('');
                if (previewData.length == 0) {
                    html += '<h4>No data</h4>';
                } else {
                    html += '<thead><tr><th>#</th><th style="width:30%;">Name / Relation</th><th>Number / Email</th><th>Message</th></tr></thead><tbody>';
                    let slno = 0;
                    for (var i = 0; i < previewData.length; i++) {
                        if(previewData[i].message_by == null || previewData[i].message_by.trim() == ''){
                            continue;
                        }
                        hasData = true;
                        var escapedMessage = previewData[i].message.replace(/"/g, '&quot;');
                        html += '<tr>';
                        html += '<td>' + (slno + 1) + '</td>';
                        html += '<td>' + previewData[i].name + ' / ' + previewData[i].relation_type + '</td>';
                        html += '<td>' + previewData[i].message_by + '</td>';
                        html += '<td>' + previewData[i].message + '</td>';
                        html += '</tr>';
                        $("#send_provision_credentials").append(`
                            <input type="hidden" name="codes[${previewData[i].pid}]" value="${previewData[i].code}">
                            <input type="hidden" name="messages[${previewData[i].std_id}_${previewData[i].pid}_${previewData[i].relation_type}_${previewData[i].user_id}_${previewData[i].send_type}]" value="${escapedMessage}">
                        `);
                        slno++;
                    }
                    if(!hasData){
                        html += '<tr><td colspan="4" class="text-center">No Contact Details Found For The Selected Users</td></tr>';
                        $("#confirmBtn").attr('disabled', true);
                    }
                    html += '</tbody></table>';
                }
                $('#dynamic-content').html(html);

                if (alreadyProv > 0) {
                    $("#warnMsg").append(`<div class="text-warning">${alreadyProv} already provisioned numbers are not taken.</div>`);
                }

                $('#modal-loader').hide();

                if (credits_available == 1) {
                    if(hasData){
                        $("#confirmBtn").attr('disabled', false);
                    }
                    $("#credit-err").hide();
                } else {
                    $("#confirmBtn").attr('disabled', true);
                    $("#credit-err").show();
                }
            })
            .fail(function() {
                $('#dynamic-content').html(
                    '<i class="glyphicon glyphicon-info-sign"></i> Something went wrong, Please try again...');
                $('#modal-loader').hide();
            });
    } else {
        $("#warnMsg").html('');
        if (alreadyProv > 0) {
            $("#warnMsg").html(alreadyProv + ' already provisioned member(s) is/are not shown here.');
        }
        $("#confirmBtn").hide();
        $('#dynamic-content').html('<i class="glyphicon glyphicon-info-sign"></i> Please select students...');
    }
}

function deactivate_provision_credentials() {
    var userIds = [];
    $('.deactivate_sendCheck:checked').each(function() {
        userIds.push($(this).val());
    });
    if (userIds.length <= 0) {
        Swal.fire({
            icon: 'info',
            title: 'No Users Selected',
            text: 'Please select at least one user to deactivate.'
        });
        return false;
    }

    Swal.fire({
        title: 'Deactivating Users',
        html: `You are deactivating <b>${userIds.length}</b> user(s). Are you sure?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes',
        cancelButtonText: 'No',
        confirmButtonColor: '#28a745', // Green
        cancelButtonColor: '#dc3545',  // Red
        customClass: {
            popup: 'medium'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo site_url('parent_activation/deactivate_provision_credentials_by_user_id'); ?>',
                type: 'POST',
                data: {
                    'userIds': userIds
                }
            })
            .done(function(data) {
                if (data) {
                    get_credentials_student_data();
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Something went wrong while deactivating users.'
                    });
                }
            })
            .fail(function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to send request to the server.'
                });
            });
        }
    });
}
</script>

<form enctype="multipart/form-data" method="post" id="send_provision_credentials" action="" class="form-horizontal">
    <div id="summary" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable" role="document">
            <div class="modal-content" style="width:80%;margin-top: 1% !important;margin:auto;">
                <div class="modal-header">
                    <h4 class="modal-title">Review the number(s) / email(s) and provide confirmation.</h4>
                    <button type="button" class="close" data-dismiss="modal" onclick="resetFrom()">&times;</button>
                </div>
                <div id="numberBody" class="modal-body table-responsive" style="overflow-y:auto;height:500px;">
                    <div>
                        <h4 id="warnMsg"></h4>
                    </div>
                    <div id="modal-loader" style="display: none; text-align: center;">
                        <!-- ajax loader -->
                        <div class="no-data-display">Please wait while the data is being prepared...</div>
                        <img src="<?php echo base_url('assets/img/ajax-loader.gif');?>" style="width:400px; height:400px;">
                    </div>
                    <table class="table table-bordered" id="dynamic-content" width="100%">

                    </table>
                </div>
                <div class="modal-footer">
                    <span id="credit-err" class="text-danger">Not enough credits to send sms</span>
                    <button type="button" id="cancelModal" class="btn btn-secondary" data-dismiss="modal" onclick="resetFrom()">Cancel</button>
                    <button type="button" id="confirmBtn" onclick="form_submit()" class="btn btn-secondary mt-0">Confirm</button>
                </div>
            </div>
        </div>
    </div>
</form>

<script type="text/javascript">
function resetFrom() {
    $('#send_provision_credentials')[0].reset();
    $('#send_provision_credentials').find('input[type=hidden]').remove();
}

function form_submit() {
    var action = "<?php echo site_url('parent_activation/send_parent_provision_credentials') ?>";
    var form = $('#send_provision_credentials');

    $("#confirmBtn").html('Please Wait...').attr('disabled', true);

    $.ajax({
        url: action,
        type: "POST",
        data: form.serialize(),
        success: function (response) {
            $("#confirmBtn").html('Confirm').attr('disabled', false);
            let parsedData = JSON.parse(response);
            if(parsedData.status == 'success'){
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: parsedData.message
                }).then((result) => {
                    $('#summary').modal('hide');
                    $('#send_provision_credentials')[0].reset();
                    $('#send_provision_credentials').find('input[type=hidden]').remove();
                    get_credentials_student_data();
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: parsedData.message
                });
            }
        },
        error: function () {
            $("#confirmBtn").html('Confirm').attr('disabled', false);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to send request to the server.'
            });
        }
    });
}
</script>