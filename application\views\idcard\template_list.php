<ul class="breadcrumb">
	<li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
	<li><a href="<?php echo site_url('idcards/Idcards_controller')?>">ID Cards</a></li>
    <li>Manage Templates</li>
</ul>
<hr>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">

            <div class="row" style="margin: 0px">
                <div style="width: 100%;" class="d-flex justify-content-between">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('idcards/Idcards_controller'); ?>">
                        <span class="fa fa-arrow-left"></span>
                        </a>
                        Manage Templates
                    </h3>
                    <a href="<?php echo site_url('create') ?>" class="btn btn-primary pull-right">
                       Create
                    </a>
                </div>
            </div>


        </div>
        <div class="card-body row">
            <?php if(empty($templates)){
                echo '<h3>Template not found</h3>';
            } ?>
            <?php foreach ($templates as $template): ?>
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><?= $template->name ?></h5>
                            <div>
                                <span class="badge badge-info">
                                    <?php
                                    $size = '';
                                    try {
                                        $design = json_decode($template->front_design, true);
                                        if ($design && isset($design['styles']['size'])) {
                                            if ($design['styles']['size'] === 'portrait') {
                                                $size = 'Portrait (54×86mm)';
                                            } elseif ($design['styles']['size'] === 'landscape') {
                                                $size = 'Landscape (86×54mm)';
                                            } elseif ($design['styles']['size'] === 'custom' && isset($design['styles']['width']) && isset($design['styles']['height'])) {
                                                $size = 'Custom ('.$design['styles']['width'].'×'.$design['styles']['height'].'mm)';
                                            }
                                        }
                                    } catch (Exception $e) {
                                        $size = 'Unknown';
                                    }
                                    echo $size;
                                    ?>
                                </span>
                                <?php if (isset($template->status)): ?>
                                    <?php if ($template->status == 'pending'): ?>
                                        <span class="badge badge-warning">Pending Approval</span>
                                    <?php elseif ($template->status == 'approved'): ?>
                                        <span class="badge badge-success">Approved</span>
                                    <?php elseif ($template->status == 'rejected'): ?>
                                        <span class="badge badge-danger">Rejected</span>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-around mb-3">
                                <div class="preview-box">
                                    <h6 class="text-center">Front</h6>
                                    <div class="template-preview front-preview" data-template-id="<?= $template->id ?>" data-side="front">
                                        <div class="preview-placeholder">
                                            <i class="fa fa-id-card fa-3x text-muted"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="preview-box">
                                    <h6 class="text-center">Back</h6>
                                    <div class="template-preview back-preview" data-template-id="<?= $template->id ?>" data-side="back">
                                        <div class="preview-placeholder">
                                            <i class="fa fa-id-card fa-3x text-muted"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="template-stats small text-muted mb-3">
                                <div><i class="fa fa-calendar-alt"></i> Created: <?= date('M d, Y', strtotime($template->created_at)) ?></div>
                                <?php if (!empty($template->updated_at)): ?>
                                    <div><i class="fa fa-edit"></i> Updated: <?= date('M d, Y', strtotime($template->updated_at)) ?></div>
                                <?php endif; ?>
                                <div><i class="fa fa-users"></i> For: <?= isset($template->id_card_for) ? $template->id_card_for : 'Both' ?></div>
                                <div><i class="fa fa-tag"></i> Unit Price: ₹<?= isset($template->unit_price) ? number_format($template->unit_price, 2) : '0.00' ?></div>
                                <?php if ($template->status == 'rejected' && !empty($template->rejection_reason)): ?>
                                    <div class="text-danger"><i class="fa fa-exclamation-circle"></i> Rejection Reason: <?= $template->rejection_reason ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="btn-group btn-group-sm w-100">
                                <a class="btn btn-outline-primary edit-template-btn" data-toggle="modal" data-target="#resource-uploader" data-template-id="<?= $template->id ?>" data-template-name="<?= $template->name ?>"  data-front-design='<?= htmlspecialchars(json_encode($template->front_design), ENT_QUOTES, 'UTF-8') ?>' data-back-design='<?= htmlspecialchars(json_encode($template->back_design), ENT_QUOTES, 'UTF-8') ?>'>
                                    <i class="fa fa-edit"></i> Edit Design
                                </a>
                                <a href="<?= site_url('edit/'.$template->id) ?>" class="btn btn-outline-primary">
                                    <i class="fa fa-edit"></i> Edit
                                </a>
                                <a href="<?= site_url('preview/'.$template->id) ?>" class="btn btn-outline-secondary">
                                    <i class="fa fa-eye"></i> Preview
                                </a>
                                <a href="javascript:void(0);" class="btn btn-outline-danger delete-template-btn" data-id="<?= $template->id ?>" data-name="<?= $template->name ?>">
                                    <i class="fa fa-trash"></i> Delete
                                </a>
                                <a href="<?= site_url('verify_data/'.$template->id) ?>" class="btn btn-outline-secondary">
                                    <i class="fa fa-eye"></i> Verify Data
                                </a>
                            </div>
                            <?php if (isset($template->status) && $template->status == 'pending'): ?>
                            <div class="btn-group btn-group-sm w-100 mt-2">
                                <a href="javascript:void(0);" class="btn btn-success approve-template-btn" data-id="<?= $template->id ?>" data-name="<?= $template->name ?>">
                                    <i class="fa fa-check"></i> Approve
                                </a>
                                <a href="javascript:void(0);" class="btn btn-danger reject-template-btn" data-id="<?= $template->id ?>" data-name="<?= $template->name ?>">
                                    <i class="fa fa-times"></i> Reject
                                </a>
                            </div>
                            <?php endif; ?>
                            <!-- <a href="<?= site_url('upload?template='.$template->id) ?>" class="btn btn-success btn-sm w-100 mt-2">
                                <i class="fa fa-id-card"></i> Use This Template
                            </a> -->
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

<div class="modal fade" id="resource-uploader" tabindex="-1" role="dialog" style="width:auto;margin:auto;top:7%" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">

      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title" id="modalHeader">Update template data - <span id="templateNameHeader"></span></h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
      <form enctype="multipart/form-data" method="post" class="form-horizontal" id="input_form">

        <div class="modal-body">
                <div class="col-md-12" style="white-space: nowrap">

                    <div class="form-group" style="padding-bottom: 8px;">
                        <label class="control-label col-md-3" for="front_design" style="margin-right: 13px;">Front Design <font color="red">*</font></label>
                        <div class="col-md-8">
                            <textarea class="form-control" id="front_design" name="front_design" rows="8" required></textarea>
                        </div>
                    </div>

                    <div class="form-group" style="padding-bottom: 8px;">
                        <label class="control-label col-md-3" for="back_design" style="margin-right: 13px;">Back Design <font color="red">*</font></label>
                        <div class="col-md-8">
                            <textarea class="form-control" id="back_design" name="back_design" rows="8" required></textarea>
                        </div>
                    </div>
                    <input type="hidden" id="template_id" name="template_id" value="">

                </div>
        </div> 

        
        <div class="modal-footer">
                <button class="btn btn-primary" onclick="update_template_design()" id="add_button" type="button">Update</button>
        </div>
      </form>

  </div>
</div>

<style>
.template-preview {
    width: 80px;
    height: 120px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background-color: #f8f9fa;
}

.preview-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.back-preview {
    background-color: #f0f0f0;
}
</style>

<script>
$(document).ready(function() {
    // Handle delete button click
    $('.delete-template-btn').click(function() {
        const templateId = $(this).data('id');
        const templateName = $(this).data('name');

        $('#templateNameToDelete').text(templateName);
        $('#confirmDeleteBtn').attr('href', '<?= site_url('template/delete/') ?>' + templateId);
        $('#deleteTemplateModal').modal('show');
    });

    // Load template previews
    $('.template-preview').each(function() {
        const $preview = $(this);
        const templateId = $preview.data('template-id');
        const side = $preview.data('side');

        // Load template data
        $.ajax({
            url: '<?= site_url('idcards/Idcards_controller/get_template_json/') ?>' + templateId,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    const template = response.template;
                    const design = side === 'front' ? template.front_design : template.back_design;

                    if (design && design.elements && design.elements.length > 0) {
                        // Clear placeholder
                        $preview.empty();

                        // Set size class
                        if (design.styles && design.styles.size) {
                            if (design.styles.size === 'portrait') {
                                $preview.css({
                                    'width': '40px',
                                    'height': '60px'
                                });
                            } else if (design.styles.size === 'landscape') {
                                $preview.css({
                                    'width': '60px',
                                    'height': '40px'
                                });
                            } else if (design.styles.size === 'custom' && design.styles.width && design.styles.height) {
                                const ratio = design.styles.width / design.styles.height;
                                if (ratio > 1) {
                                    $preview.css({
                                        'width': '60px',
                                        'height': (60 / ratio) + 'px'
                                    });
                                } else {
                                    $preview.css({
                                        'width': (60 * ratio) + 'px',
                                        'height': '60px'
                                    });
                                }
                            }
                        }

                        // Create content container
                        const $content = $('<div class="preview-content"></div>');
                        $content.css({
                            'position': 'relative',
                            'width': '100%',
                            'height': '100%',
                            'background-color': '#fff'
                        });

                        // Add a simplified preview
                        $content.append('<div class="text-center text-muted small" style="padding:5px;font-size:6px;">' +
                            design.elements.length + ' elements</div>');

                        $preview.append($content);
                    }
                }
            }
        });
    });

    $('.delete-template').click(function() {
        const id = $(this).data('id');
        if (confirm('Are you sure you want to delete this template?')) {
            window.location.href = `<?= site_url('templates/delete/') ?>${id}`;
        }
    });

    // Handle approve button click
    $('.approve-template-btn').click(function() {
        const templateId = $(this).data('id');
        const templateName = $(this).data('name');

        $('#templateNameToApprove').text(templateName);
        $('#approveTemplateId').val(templateId);
        $('#approveTemplateModal').modal('show');
    });

    // Handle reject button click
    $('.reject-template-btn').click(function() {
        const templateId = $(this).data('id');
        const templateName = $(this).data('name');

        $('#templateNameToReject').text(templateName);
        $('#rejectTemplateId').val(templateId);
        $('#rejectTemplateModal').modal('show');
    });

    // Handle approve form submission
    $('#approveTemplateForm').submit(function(e) {
        e.preventDefault();
        const templateId = $('#approveTemplateId').val();

        $.ajax({
            url: '<?= site_url("idcards/Idcards_controller/approve_template") ?>',
            type: 'POST',
            data: { template_id: templateId },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $('#approveTemplateModal').modal('hide');
                    alert(response.message);
                    location.reload();
                } else {
                    alert(response.message || 'Failed to approve template');
                }
            },
            error: function() {
                alert('An error occurred while approving the template');
            }
        });
    });

    // Handle reject form submission
    $('#rejectTemplateForm').submit(function(e) {
        e.preventDefault();
        const templateId = $('#rejectTemplateId').val();
        const reason = $('#rejectionReason').val();

        if (!reason.trim()) {
            alert('Please provide a reason for rejection');
            return;
        }

        $.ajax({
            url: '<?= site_url("idcards/Idcards_controller/reject_template") ?>',
            type: 'POST',
            data: {
                template_id: templateId,
                reason: reason
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $('#rejectTemplateModal').modal('hide');
                    alert(response.message);
                    location.reload();
                } else {
                    alert(response.message || 'Failed to reject template');
                }
            },
            error: function() {
                alert('An error occurred while rejecting the template');
            }
        });
    });
});

$(document).on('click', '.edit-template-btn', function () {
    const $this = $(this);

    const front = JSON.parse($this.attr('data-front-design') || '""');
    const back = JSON.parse($this.attr('data-back-design') || '""');
    $('#template_id').val($this.data('template-id'));
    $('#front_design').val(front);
    $('#back_design').val(back);
    $('#modalHeader').text('Edit template data');
    $('#add_button').text('Update');
    $('#templateNameHeader').text($this.data('template-name'));
});



function update_template_design(){
    const formElement = $('#input_form')[0];
    const formData = new FormData(formElement);

    Swal.fire({
        title: 'Are you sure?',
        text: "Do you want to update the template data?",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, submit it!'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                title: 'Submitting...',
                text: 'Please wait while we process your request.',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });

            $.ajax({
                url: '<?php echo site_url('idcards/idcards_controller/update_template_design'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                cache: false,
                success: function(data) {
                    if(data==1){
                        Swal.fire({
                            title: 'Success!',
                            text: 'Template data has been updated successfully.',
                            icon: 'success'
                        }).then((result) => {
                                location.reload();
                        });
                    }else{
                        Swal.fire({
                            title: 'Error!',
                            text: 'An error occurred while updating the template data.',
                            icon: 'error'
                        });
                    }
                },
                error: function (err) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'An error occurred while updating the template data.',
                        icon: 'error'
                    });
                    console.log(err);
                }
            });
        }
    });
}
</script>

<!-- Delete Template Modal -->
<div class="modal fade" id="deleteTemplateModal" tabindex="-1" role="dialog" aria-labelledby="deleteTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteTemplateModalLabel">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete the template <strong id="templateNameToDelete"></strong>?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger">Delete</a>
            </div>
        </div>
    </div>
</div>

<!-- Approve Template Modal -->
<div class="modal fade" id="approveTemplateModal" tabindex="-1" role="dialog" aria-labelledby="approveTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approveTemplateModalLabel">Confirm Approval</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="approveTemplateForm">
                <div class="modal-body">
                    <p>Are you sure you want to approve the template <strong id="templateNameToApprove"></strong>?</p>
                    <input type="hidden" id="approveTemplateId" name="template_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Approve</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reject Template Modal -->
<div class="modal fade" id="rejectTemplateModal" tabindex="-1" role="dialog" aria-labelledby="rejectTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectTemplateModalLabel">Confirm Rejection</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="rejectTemplateForm">
                <div class="modal-body">
                    <p>Are you sure you want to reject the template <strong id="templateNameToReject"></strong>?</p>
                    <div class="form-group">
                        <label for="rejectionReason">Reason for Rejection</label>
                        <textarea class="form-control" id="rejectionReason" name="reason" rows="3" required></textarea>
                    </div>
                    <input type="hidden" id="rejectTemplateId" name="template_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Reject</button>
                </div>
            </form>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
