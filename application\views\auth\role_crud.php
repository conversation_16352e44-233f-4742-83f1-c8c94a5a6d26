<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('SchoolAdmin_menu');?>">Manage Roles & Privileges</a></li>
  <li> Roles Management</li>
</ul>

<div class="col-md-12">
  <div class="card cd_border" style="padding-bottom:2px;">
    <div class="card-header panel_heading_new_style_staff_border_v2">
      <div class="row align-items-center" style="margin: 0px;">
        <div class="col-md-4 d-flex align-items-center">
          <h3 class="card-title panel_title_new_style_staff mb-2 mr-3">
            <a class="back_anchor" href="<?php echo site_url('SchoolAdmin_menu');?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
            Roles Management
          </h3>
        </div>

        <div class="col-md-8 text-right d-flex align-items-center justify-content-end">
          <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createRole" onclick="open_create_role_modal()" style="margin-right: 5px;">
            Create Role
          </button>
          <?php if($showLogout) { ?>
						<button class="btn btn-primary" onclick="logout()">Logout</button>
					<?php } ?>
        </div>
      </div>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table datatable table-bordered">
          <thead>
            <th style="width:2%;">#</th>
            <th style="width:20%;">Role Name</th>
            <th style="width:40%;">Description</th>
            <th style="width:20%;">Assigned Staff</th>
            <th style="width:10%;">Status</th>
            <th style="width:8%;">Actions</th>
          </thead>
          <tbody>
            <?php $i=1; foreach ($roles as $roleId => $role) { ?>
              <tr>
                <td><?= $i++; ?></td>
                <td><?= $role['name'] ?></td>
                <td><?= $role['description'] ?></td>
                <td>
                  <div style="max-height: 80px; overflow-y: auto; font-size: 12px;">
                    <?= implode("<br>", $role['staff']) ?>
                  </div>
                </td>
                <td>
                  <label class="switch">
                    <input name="active_inactive" onclick="switchRoleMode(<?= $roleId;?>)" id="activeInactive<?= $roleId;?>" type="checkbox" <?php if($role['active'] =='1') echo "checked" ?>  value="<?= $role['active']?>"/>
                    <span></span>
                  </label>
                  <span id="statusText<?= $roleId;?>"></span>
                </td>
                <td>
                  <a  href="<?php echo site_url('roles/add_staff_privileges/'.$roleId);?>" class='btn btn-warning ' data-placement='top' <?php if($role['active'] =='0') echo "disabled" ?> data-toggle='tooltip' data-original-title='Add Permission to Users'>
                    <i class='fa fa-plus'></i>
                  </a>
                </td>
              </tr>
            <?php } ?>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<style>
  .bootbox > .modal-dialog{
    width: 60%;
    margin: auto;
    top: 10%;
  }
</style>

<script type="text/javascript">
  function logout(){
		bootbox.confirm({
			title: "Logout",
			message: "Are you sure, you want to logout from Role Management Console?",
			buttons: {
				confirm: {
					label: 'Yes',
					className: 'btn-success'
				},
				cancel: {
					label: 'No',
					className: 'btn-danger'
				}
			},
			callback: function (result) {
				if(result){
					window.location.href = '<?php echo site_url("roles/logout") ?>';
				}
			}
		})
	}

  function switchRoleMode($id) {  
    var role_id=$id;
    var $checkbox = $('#activeInactive' + role_id);
    var mode = $('#activeInactive'+role_id).val();
    $checkbox.prop('disabled', true);
    let statusSpanId = '#statusText' + role_id;
    $(statusSpanId).text('Refresh to view Chnages...');

    $.ajax({
      url: "<?php echo site_url('roles/switch_role_mode');?>",
      data: { 'role_id': role_id, 'mode': mode },
      type: 'post',
      success: function(data) {
        // location.reload();
      },
      error: function(err) {
          console.log(err);
          $checkbox.prop('disabled', false);
          $(statusSpanId).text('Error. Try again.');
      }
    });
  }

  function open_create_role_modal() {
    bootbox.dialog({
        title: 'Create New Role',
        message: `
            <div id="role_error" style="color: red; margin-bottom: 10px;"></div>
            <div class="form-group">
                <label for="role_name">Name <font color="red">*</font></label>
                <input type="text" class="form-control" name="role_name" id="role_name" placeholder="Enter Name">
            </div>
            <div class="form-group">
                <label for="description">Description <font color="red">*</font></label>
                <input type="text" class="form-control" name="description" id="description" placeholder="Enter Description">
            </div>
        `,
        buttons: {
            cancel: {
                label: "Cancel",
                className: 'btn-danger'
            },
            confirm: {
                label: "Create",
                className: 'btn-success',
                callback: function () {
                    const roleName = $('#role_name').val()?.trim();
                    const roleDesc = $('#description').val()?.trim();
                    const errorBox = $('#role_error');

                    // Clear previous error
                    errorBox.html('');

                    if (!roleName) {
                        errorBox.html("Role Name is required.");
                        return false;
                    }
                    if (!roleDesc) {
                        errorBox.html("Role Description is required.");
                        return false;
                    }

                    $.ajax({
                        url: '<?= site_url('roles/submit_role') ?>',
                        type: 'POST',
                        data: {
                            role_name: roleName,
                            description: roleDesc
                        },
                        success: function (response) {
                            new PNotify({
                                title: 'Success',
                                text: 'Role created successfully. Please refresh to view changes',
                                type: 'success'
                            });
                        },
                        error: function () {
                            new PNotify({
                                title: 'Error',
                                text: 'Failed to create role.',
                                type: 'error'
                            });
                        }
                    });
                }
            }
        }
    });

    // Apply custom styles to the modal
    setTimeout(() => {
        $('.bootbox .modal-dialog').css({
            'width': '50%',
            'margin-left': '25%'
        });
    }, 50);
  }
</script>