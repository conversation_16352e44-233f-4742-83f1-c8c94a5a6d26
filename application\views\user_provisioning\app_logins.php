<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('SchoolAdmin_menu');?>">Manage User</a></li>
    <li>Parent Login Report</li>
</ul>

<div class="col-md-12 col_new_padding">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="col-md-6 pl-0">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('SchoolAdmin_menu') ?>"
                            class="control-primary">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Parent Login Report
                    </h3>
                </div>
                <div class="col-md-6">
                    <select id="admission_type" name="admission_type" onchange="getData()" required class="form-control custom-select input-md col-md-3 pull-right">
                        <option value=""><?php echo "All" ?></option>
                        <?php foreach ($admission_type as $value => $type) { ?>
                            <option value="<?php echo $value ?>"><?php echo $type ?></option>
                        <?php } ?>
                    </select>
                </div>
            </div>
        </div>
        <div class="card-body pt-0">
            <div class="row" style="margin-left:0px;margin-right:0px;margin-bottom: 1.6rem">
                <div class="col">
                    <div class="form-group text-center"
                        style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px;">
                        <h5>Search By Class</h5>
                        <div class="row d-flex" style="margin: 0px">
                            <div class="col-md-8 col-md-offset-2">
                                <?php 
                                    $array = array();
                                    $array[0] = 'Select Class';
                                    $array[-1] = 'All Class';
                                    foreach ($classList as $key => $cl) {
                                        $array[$cl->classId] = $cl->className;
                                    }
                                    echo form_dropdown("classId", $array, set_value("classId",$selectedClassId), "id='classId' onchange='class_wise_search_std();' class='form-control custom-select'");
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group text-center"
                        style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px;">
                        <h5>Search By Class / Section</h5>
                        <div class="row">
                            <div class="col-md-8 col-md-offset-2">
                                <?php 
                                    $array = array();
                                    $array[0] = 'Select Section';
                                    foreach ($classSectionList as $key => $cl) {
                                        $array[$cl->id] = $cl->class_name . $cl->section_name;
                                    }
                                    echo form_dropdown("classSectionId", $array, '', "id='classSectionId'  onchange='classSection_wise_search_std()'  class='form-control custom-select'");
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group text-center hidden-xs"
                        style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px;">
                        <div class="form-horizontal">
                            <h5>Search By Student Name</h5>
                            <div class="row">
                                <div class="col-md-8 col-md-offset-2">
                                    <input id="stdName1" autocomplete="off" placeholder="Search by Student Name"
                                        class="form-control input-md" name="stdName1">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" style="margin-left: 0px;margin-right: 0px;margin-bottom: 8px">
                <div class="col">
                    <div class="form-group text-center hidden-xs"
                        style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px;">
                        <div class="form-horizontal">
                            <h5>Search By Student Admission No</h5>
                            <div class="row">
                                <div class="col-md-6 col-md-offset-2">
                                    <input id="admission_no" autocomplete="off" placeholder="Search by Admission No"
                                        class="form-control input-md" name="admission_no">
                                </div>
                                <div class="col-md-2">
                                    <input type="button" value="Get" id="getByAdmissionNo"
                                        class="input-md btn btn-primary">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group text-center hidden-xs"
                        style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px;">
                        <div class="form-horizontal">
                            <h5>Search By Phone No</h5>
                            <div class="row">
                                <div class="col-md-6 col-md-offset-2">
                                    <input id="phone_no" autocomplete="off" placeholder="Search by Phone No"
                                        class="form-control input-md" name="phone_no">
                                </div>
                                <div class="col-md-2">
                                    <input type="button" value="Get" id="getByPhoneNo" class="input-md btn btn-primary">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group text-center hidden-xs"
                        style="border-radius:5px;  box-shadow:0px 2px 6px #ccc;padding:10px 5px;">
                        <div class="form-horizontal">
                            <h5>Search By Email id</h5>
                            <div class="row">
                                <div class="col-md-6 col-md-offset-2">
                                    <input id="emailId" autocomplete="off" placeholder="Search by Email id"
                                        class="form-control input-md" name="email_id">
                                </div>
                                <div class="col-md-2">
                                    <input type="button" value="Get" id="getByEmailId" class="input-md btn btn-primary">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body pt-0">
            <div class="col-md-12">
                <div style="display: flex; justify-content: end; align-items: center; width: 100%;">
                    <div style="display: flex; align-items: center; gap: 15px;">
                        <label class="control-label" style="margin: 0; display: flex; align-items: center;">
                            Show Activated but not logged-in
                            <input style="width: 20px; height: 20px; margin-left: 10px;" onchange="show_no_loggins()"
                                type="checkbox" id="show-no-loggins">
                        </label>
                    </div>
                </div>
                <div id="printArea" class="panel-body stdudentData leaveData px-0">
                    <h3 class="no-data-display">Select filter to view data</h3>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function show_no_loggins() {
    if($("#show-no-loggins").is(':checked')) {
        $(".all-logins").hide();
        $(".no-loggins").show();
    } else {
        $(".all-logins").show();
    }
}

function admission_status_change() {
    var sectionId = $("#classSectionId").val();
    if (sectionId !=0) {
        classSection_wise_search_std();
    }else{
        class_wise_search_std();
    }
}

function class_wise_search_std() {
    $(".stdudentData").html('<div class="no-data-display">Loading...</div>');
    var classId = $("#classId").val();
    var adm_status = $('#admission_status').val();
    let admission_type = $('#admission_type').val();
    $("#show-no-loggins").prop('checked', false);
    if(classId) {
        $.ajax({
            url: '<?php echo site_url('user_provisioning_controller/getStudentDetails'); ?>',
            type: 'post',
            data: {'classId':classId, 'mode':'class_id', 'admission_type':admission_type },
            success: function(data) {
                var std = JSON.parse(data);
                $(".stdudentData").html(prepare_student_table(std));
                $('#customers2').DataTable({
                    destroy: true,            // Destroy any previous instance
                    paging: false,            // No pagination
                    ordering: false,          // No column ordering
                    searching: true,         // No search box
                    lengthChange: false,      // No length dropdown
                    info: false,              // No "Showing x of y" info
                    dom: 'lBfrtip',           // Enable Buttons extension
                    buttons: [
                        {
                            extend: 'excel',
                            text: 'Excel',
                            className: 'btn btn-success',
                            filename: 'Parent Login Report'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            className: 'btn btn-primary',
                            filename: 'Parent Login Report'
                        }
                    ]
                });
            }
        });
    }
}

function classSection_wise_search_std() {
    $(".stdudentData").html('<div class="no-data-display">Loading...</div>');
    var sectionId = $("#classSectionId").val();
    var adm_status = $('#admission_status').val();
    let admission_type = $('#admission_type').val();
    $("#show-no-loggins").prop('checked', false);
    if(sectionId) {
        $.ajax({
            url: '<?php echo site_url('user_provisioning_controller/getStudentDetails'); ?>',
            type: 'post',
            data: {'sectionId':sectionId, 'mode':'section_id', 'admission_type': admission_type},
            success: function(data) {
                var std = JSON.parse(data);
                $(".stdudentData").html(prepare_student_table(std));
                $('#customers2').DataTable({
                    destroy: true,            // Destroy any previous instance
                    paging: false,            // No pagination
                    ordering: false,          // No column ordering
                    searching: true,         // No search box
                    lengthChange: false,      // No length dropdown
                    info: false,              // No "Showing x of y" info
                    dom: 'lBfrtip',           // Enable Buttons extension
                    buttons: [
                        {
                            extend: 'excel',
                            text: 'Excel',
                            className: 'btn btn-success',
                            filename: 'Parent Login Report'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            className: 'btn btn-primary',
                            filename: 'Parent Login Report'
                        }
                    ]
                });
            }
        });
    }
}

var names = [];
$(document).ready(function(){
    var stdNames = JSON.parse('<?php echo json_encode($studentNames); ?>');
    for(var i=0; i<stdNames.length; i++){
        names.push(stdNames[i].stdName);
    }
    $("#getByAdmissionNo").click(function(){
        $(".stdudentData").html('<div class="no-data-display">Loading...</div>');
        var admin_no = $("#admission_no").val();
        let admission_type = $('#admission_type').val();
        $("#show-no-loggins").prop('checked', false);
        if(admin_no) {
            $.ajax({
                url: '<?php echo site_url('user_provisioning_controller/getStudentDetails'); ?>',
                type: 'post',
                data: {'ad_no':admin_no, 'mode':'ad_no', 'admission_type':admission_type},
                success: function(data) {
                    var std = JSON.parse(data);
                    $(".stdudentData").html(prepare_student_table(std));
                    $('#customers2').DataTable({
                        destroy: true,            // Destroy any previous instance
                        paging: false,            // No pagination
                        ordering: false,          // No column ordering
                        searching: true,         // No search box
                        lengthChange: false,      // No length dropdown
                        info: false,              // No "Showing x of y" info
                        dom: 'lBfrtip',           // Enable Buttons extension
                        buttons: [
                            {
                                extend: 'excel',
                                text: 'Excel',
                                className: 'btn btn-success',
                                filename: 'Parent Login Report'
                            },
                            {
                                extend: 'print',
                                text: 'Print',
                                className: 'btn btn-primary',
                                filename: 'Parent Login Report'
                            }
                        ]
                    });
                }
            });
        }
    });

    $("#getByPhoneNo").click(function(){
        $(".stdudentData").html('<div class="no-data-display">Loading...</div>');
        var phone_no = $("#phone_no").val();
        let admission_type = $('#admission_type').val();
        $("#show-no-loggins").prop('checked', false);
        if(phone_no) {
            $.ajax({
                url: '<?php echo site_url('user_provisioning_controller/getStudentDetails'); ?>',
                type: 'post',
                data: {'phone_no':phone_no, 'mode':'phone_no', 'admission_type':admission_type},
                success: function(data) {
                    var std = JSON.parse(data);
                    $(".stdudentData").html(prepare_student_table(std));
                    $('#customers2').DataTable({
                        destroy: true,            // Destroy any previous instance
                        paging: false,            // No pagination
                        ordering: false,          // No column ordering
                        searching: true,         // No search box
                        lengthChange: false,      // No length dropdown
                        info: false,              // No "Showing x of y" info
                        dom: 'lBfrtip',           // Enable Buttons extension
                        buttons: [
                            {
                                extend: 'excel',
                                text: 'Excel',
                                className: 'btn btn-success',
                                filename: 'Parent Login Report'
                            },
                            {
                                extend: 'print',
                                text: 'Print',
                                className: 'btn btn-primary',
                                filename: 'Parent Login Report'
                            }
                        ]
                    });
                }
            });
        }
    });

    $("#getByEmailId").click(function(){
        $(".stdudentData").html('<div class="no-data-display">Loading...</div>');
        var email = $("#emailId").val();
        let admission_type = $('#admission_type').val();
        $("#show-no-loggins").prop('checked', false);
        if(email) {
            $.ajax({
                url: '<?php echo site_url('user_provisioning_controller/getStudentDetails'); ?>',
                type: 'post',
                data: {'email':email, 'mode':'email', 'admission_type':admission_type},
                success: function(data) {
                    var std = JSON.parse(data);
                    $(".stdudentData").html(prepare_student_table(std));
                    $('#customers2').DataTable({
                        destroy: true,            // Destroy any previous instance
                        paging: false,            // No pagination
                        ordering: false,          // No column ordering
                        searching: true,         // No search box
                        lengthChange: false,      // No length dropdown
                        info: false,              // No "Showing x of y" info
                        dom: 'lBfrtip',           // Enable Buttons extension
                        buttons: [
                            {
                                extend: 'excel',
                                text: 'Excel',
                                className: 'btn btn-success',
                                filename: 'Parent Login Report'
                            },
                            {
                                extend: 'print',
                                text: 'Print',
                                className: 'btn btn-primary',
                                filename: 'Parent Login Report'
                            }
                        ]
                    });
                }
            });
        }
    });
});

function getData(){
    var email = $("#emailId").val();

    var phone_no = $("#phone_no").val();

    var admin_no = $("#admission_no").val();

    var sectionId = $("#classSectionId").val();

    var classId = $("#classId").val();

    if (admin_no.trim() != '') {
        $("#getByAdmissionNo").trigger('click');
    } else if (phone_no.trim() != '') {
        $("#getByPhoneNo").trigger('click');
    } else if (email.trim() != '') {
        $("#getByEmailId").trigger('click');
    } else if (sectionId != 0) {
        classSection_wise_search_std();
    } else if (classId != 0) {
        class_wise_search_std();
    }
}

autocomplete(document.getElementById("stdName1"), names);
var stdName = '';

function autocomplete(inp, arr) {
    /*the autocomplete function takes two arguments,
    the text field element and an array of possible autocompleted values:*/
    var currentFocus;
    /*execute a function when someone writes in the text field:*/
    inp.addEventListener("input", function(e) {
        $(".stdudentData").html('<div class="no-data-display">Loading...</div>');
        var a, b, i, val = this.value;
        /*close any already open lists of autocompleted values*/
        closeAllLists();
        if (!val) { return false;}
        currentFocus = -1;
        /*create a DIV element that will contain the items (values):*/
        a = document.createElement("DIV");
        a.setAttribute("id", this.id + "autocomplete-list");
        a.setAttribute("class", "autocomplete-items");
        /*append the DIV element as a child of the autocomplete container:*/
        this.parentNode.appendChild(a);
        /*for each item in the array...*/
        for (i = 0; i < arr.length; i++) {
            /*check if the item starts with the same letters as the text field value:*/
            if (arr[i].substr(0, val.length).toUpperCase() == val.toUpperCase()) {
                /*create a DIV element for each matching element:*/
                b = document.createElement("DIV");
                /*make the matching letters bold:*/
                b.innerHTML = "<strong>" + arr[i].substr(0, val.length) + "</strong>";
                b.innerHTML += arr[i].substr(val.length);
                /*insert a input field that will hold the current array item's value:*/
                b.innerHTML += "<input type='hidden' value='" + arr[i] + "'>";
                /*execute a function when someone clicks on the item value (DIV element):*/
                b.addEventListener("click", function(e) {
                    /*insert the value for the autocomplete text field:*/
                    inp.value = this.getElementsByTagName("input")[0].value;
                    stdName = this.getElementsByTagName("input")[0].value;
                    $("#show-no-loggins").prop('checked', false);
                    let admission_type = $('#admission_type').val();
                    $.ajax({
                        url: '<?php echo site_url('user_provisioning_controller/getStudentDetails'); ?>',
                        type: 'post',
                        data: {'name':stdName, 'mode':'name', 'admission_type':admission_type},
                        success: function(data) {
                            var std = JSON.parse(data);
                            $(".stdudentData").html(prepare_student_table(std));
                            $('#customers2').DataTable({
                                destroy: true,            // Destroy any previous instance
                                paging: false,            // No pagination
                                ordering: false,          // No column ordering
                                searching: true,         // No search box
                                lengthChange: false,      // No length dropdown
                                info: false,              // No "Showing x of y" info
                                dom: 'lBfrtip',           // Enable Buttons extension
                                buttons: [
                                    {
                                        extend: 'excel',
                                        text: 'Excel',
                                        className: 'btn btn-success',
                                        filename: 'Parent Login Report'
                                    },
                                    {
                                        extend: 'print',
                                        text: 'Print',
                                        className: 'btn btn-primary',
                                        filename: 'Parent Login Report'
                                    }
                                ]
                            });
                        }
                    });
                    /*close the list of autocompleted values,
                    (or any other open lists of autocompleted values:*/
                    closeAllLists();
                });
            a.appendChild(b);
            }
        }
    });
    /*execute a function presses a key on the keyboard:*/
    inp.addEventListener("keydown", function(e) {
        var x = document.getElementById(this.id + "autocomplete-list");
        if (x) x = x.getElementsByTagName("div");
        if (e.keyCode == 40) {
            /*If the arrow DOWN key is pressed,
            increase the currentFocus variable:*/
            currentFocus++;
            /*and and make the current item more visible:*/
            addActive(x);
        } else if (e.keyCode == 38) { //up
            /*If the arrow UP key is pressed,
            decrease the currentFocus variable:*/
            currentFocus--;
            /*and and make the current item more visible:*/
            addActive(x);
        } else if (e.keyCode == 13) {
            /*If the ENTER key is pressed, prevent the form from being submitted,*/
            e.preventDefault();
            if (currentFocus > -1) {
            /*and simulate a click on the "active" item:*/
            if (x) x[currentFocus].click();
            }
        }
    });
    function addActive(x) {
        /*a function to classify an item as "active":*/
        if (!x) return false;
        /*start by removing the "active" class on all items:*/
        removeActive(x);
        if (currentFocus >= x.length) currentFocus = 0;
        if (currentFocus < 0) currentFocus = (x.length - 1);
        /*add class "autocomplete-active":*/
        x[currentFocus].classList.add("autocomplete-active");
    }
    function removeActive(x) {
        /*a function to remove the "active" class from all autocomplete items:*/
        for (var i = 0; i < x.length; i++) {
        x[i].classList.remove("autocomplete-active");
        }
    }
    function closeAllLists(elmnt) {
        /*close all autocomplete lists in the document,
        except the one passed as an argument:*/
        var x = document.getElementsByClassName("autocomplete-items");
        for (var i = 0; i < x.length; i++) {
            if (elmnt != x[i] && elmnt != inp) {
            x[i].parentNode.removeChild(x[i]);
            }
        }
    }
    /*execute a function when someone clicks in the document:*/
    document.addEventListener("click", function (e) {
        closeAllLists(e.target);
    });
}

function prepare_student_table (std) {
    var path = '<?php echo $this->filemanager->getFilePath(""); ?>';
    var picUrl = '';
    var html = '';
    
    if(!std)
        html += "<h4>Select any filter to get student data</h4>";
    else {
        html += `<table id="customers2" class="table table-bordered">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Student Name</th>
                            <th>Adm No</th>
                            <th>Section</th>
                            <th>Relation</th>
                            <th>Username</th>
                            <th>Details</th>
                            <th>Status</th>
                            <th>Last accessed</th>
                            <th>Can Recieve Notification</th>
                        </tr>
                    </thead>
                    <tbody>`;
        var j = 0;
        for (var i in std) {
            picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/femalestu.png';
            if(std[i].gender == 'Male'){
                picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/malestu.png';
            }

            if(std[i].picture_url != '' && std[i].picture_url != null) {
                picUrl = path + std[i].picture_url;
            }

            var parents = std[i].parents;
            // var rows = Object.keys(parents).length;
            var noLoggins = '';
            if(std[i].loggedin_atleast_once == 0 && std[i].Active == 1) {
                noLoggins = 'no-loggins';
            } 
            else{
                noLoggins = 'all-logins';
            }

            html += "<tr class='"+noLoggins+"'><td>" + (++j) + "</td>";
            // html += "<td></td>";
            html += "<td><img width='30' height='30' class='img-circle' src='"+picUrl+"' />&nbsp;&nbsp;" + std[i].studentName + "</td>";
            html += "<td>" + (std[i].admission_no ? std[i].admission_no : 'NA') + "</td>";
            html += "<td>" + std[i].class_name + ""+ std[i].sectionName + "</td>";
            html += `<td>${std[i].relation_type}</td>`;
            html += `<td>${std[i].username}</td>`;
            html += `<td><b>${std[i].parentName}</b> (${std[i].mobile ? std[i].mobile : 'NA'})</td>`;
            html += `<td>
                        ${
                            std[i].loggedin_atleast_once == '1' && std[i].Active == '1' ? 
                            '<b class="text-success">Logged In</b>' : std[i].Active == '1' && std[i].loggedin_atleast_once == '0' ? 
                            '<b class="text-warning">Activated<br>Not Logged In</b>' :
                            '<b class="text-danger">Not Activated</b>'
                        }
                    </td>`;
            html += `<td>${std[i].last_login != null ? std[i].last_login : '-'}</td>`;
            html += `<td>
                        ${
                            (std[i].token != null && std[i].token != '' ) ? 
                            '<b class="text-success">Yes</b>' : 
                            '<b class="text-danger">No</b>'
                        }
                    </td>`;
            html += "</tr>";
        }
        html += '</tbody></table>';
    }
    return html;
}
</script>

<style>
#tags{
    position:relative
    padding: 10px;
}
.autocomplete-items {
    position: absolute;
    overflow-y:auto;
    border-bottom: none;
    border-top: none;
    height:300px;
    margin:0px 15px;
    z-index: 99;
    /*position the autocomplete items to be the same width as the container:*/
    top: 100%;
    left: 0;
    right: 0;
}
.autocomplete-items div {
    padding: 10px;
    cursor: pointer;
    background-color: #fff; 
    border-bottom: 1px solid #d4d4d4; 
}
.autocomplete-items div:hover {
    /*when hovering an item:*/
    background-color: #e9e9e9; 
}
.autocomplete-active {
    /*when navigating through the items using the arrow keys:*/
    background-color: DodgerBlue !important; 
    color: #ffffff; 
}
ul.panel-controls>li>a {
    border-radius: 50%;
}
.dt-buttons{
    float: right;
    margin-bottom: 5px;
}
#customers2_filter{
    float: left !important;
}
#customers2_filter label{
    float: left !important;
}
</style>