<?php

class Roles extends CI_Controller {

    function __construct() {
        parent::__construct();
        $this->load->model('role');
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isAuthorized('USER_MANAGEMENT.ASSIGN_ROLES_AND_PRIVILEGES')) {
            redirect('dashboard', 'refresh');
        }
    }

    public function login() {
        $user_data = $this->role->get_username_email($this->authorization->getAvatarId());
        if($user_data) {
            $data['email_id'] = isset($user_data->email) ? $user_data->email : '';
            $data['name'] = isset($user_data->staff_name) ? $user_data->staff_name : '';
        } else {
            $data['email_id'] = '';
            $data['name'] = '';
        }
        $data['main_content'] = 'auth/role_login';
        $this->load->view('inc/template', $data);
    }

    public function logout() {
        $this->session->unset_userdata('user_logged_in');
        $this->session->unset_userdata('loginstatus');
        $this->session->unset_userdata('user_id');
        $this->session->unset_userdata('email');
        $this->session->unset_userdata('otp_verified');
        redirect('roles');
    }

    public function send_otp() {
        $input = $this->input->post();
        $otp = rand(100000,999999);
        $smsint = $this->settings->getSetting('smsintergration');
        $msg = $otp. ' is your One Time Password for verification as an applicant for registration-Nextelement';
        $from_name = $this->settings->getSetting('school_name');
        $emailMessage = '
                Dear Applicant,
                <br>
                Your OTP for User Management Login is '.$otp.'
                <br>
                Thanks and Regards, <br>
                -'.$from_name.'';
        $emailId = $input['email'];
        $data = array(
            'send_unique_emails' => 1,
            'message' => [$emailMessage],
            'subject' => 'OTP for User Management',
            'mail_username' => ['NextElement'],
            'email_ids' => [$emailId],
            'template' => 'Admission OTP'
        );
        $res = $this->__email_one_time_verification_application($data);
        if($res) {
            $this->role->insertOTP($input, $otp);
            echo json_encode(['status' => 'ok','msg' => 'Email Sent!']);
        } else {
            echo json_encode(['status' => 'error','msg' => 'Unable to send Email please try again!' ]);
        }
    }

    private function __email_one_time_verification_application($data){
        $from_name = $this->settings->getSetting('school_name');    
        $from_email = $this->settings->getSetting('admisison_one_time_password_send_email_id');   
        $smtp_user = CONFIG_ENV['smtp_user'];
        $smtp_pass = urlencode(CONFIG_ENV['smtp_pass']);
        $smtp_host = CONFIG_ENV['smtp_host'];
        $smtp_port = CONFIG_ENV['smtp_port'];
        $data['from_email'] = "<EMAIL>";
        $data['from_name'] = $from_name;
        $data['smtp_user'] = $smtp_user;
        $data['smtp_pass'] = $smtp_pass;
        $data['smtp_host'] = $smtp_host;
        $data['smtp_port'] = $smtp_port;
        $data = http_build_query($data);
        $curl = curl_init();
        $username = CONFIG_ENV['job_server_username'];
        $password = CONFIG_ENV['job_server_password'];
        curl_setopt_array($curl, array(
            CURLOPT_URL => CONFIG_ENV['job_server_immediate_email_uri'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERPWD => $username . ":" . $password,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POST => 1,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => array(
                "Accept: application/json",
                "Cache-Control: no-cache",
                "Content-Type: application/x-www-form-urlencoded",
                "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
            ),
        ));
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        if ($err) {
            return 0;
        } else {
            return 1;
        }
    }
    
    public function verify_otp() {
        $input = $this->input->post();

        if ($this->role->verifyOTP($input)) {
            $this->session->set_userdata('user_logged_in', true);
            $this->session->set_userdata('otp_verified', true);
            $this->session->set_userdata('email', $input['email']);
            $this->session->set_userdata('name', $input['name']);
            $this->session->set_userdata('last_activity', time());

            echo json_encode(['status' => 'ok']);
        } else {
            echo json_encode(['status' => 'error', 'msg' => 'Incorrect OTP. Please try again.']);
        }
    }
    //Landing function for viewing and adding roles
    public function index() {
        $showLogout = false;
        if ($this->authorization->isSuperAdmin()) {
            if (ENVIRONMENT !== 'development') {
                if (!$this->session->userdata('user_logged_in') || !$this->session->userdata('otp_verified')) {
                    redirect('roles/login');
                }

                $timeout = 1800;
                if ($this->session->userdata('last_activity') && time() - $this->session->userdata('last_activity') > $timeout) {
                    $this->session->unset_userdata('loginstatus');
                    $this->session->unset_userdata('user_id');
                    $this->session->unset_userdata('email');
                    $this->session->unset_userdata('otp_verified');
                    redirect('roles/login');
                }

                $this->session->set_userdata('last_activity', time());
                $data['email'] = $this->session->userdata('email');
                $data['name'] = $this->session->userdata('name');
                $showLogout = true;
            } else {
                $data['email'] = '<EMAIL>';
                $data['name'] = 'Dev User';
                $showLogout = true;
            }
        } else {
            if ($this->settings->getSetting('enable_mfa_for_critical_features') == 1 && $this->authorization->getAvatarType() == 4) {
                if (!$this->session->userdata('user_logged_in') || !$this->session->userdata('otp_verified')) {
                    redirect('roles/login');
                }

                $timeout = 1800;
                if ($this->session->userdata('last_activity') && time() - $this->session->userdata('last_activity') > $timeout) {
                    $this->session->unset_userdata('loginstatus');
                    $this->session->unset_userdata('user_id');
                    $this->session->unset_userdata('email');
                    $this->session->unset_userdata('otp_verified');
                    redirect('roles/login');
                }

                $this->session->set_userdata('last_activity', time());
                $data['email'] = $this->session->userdata('email');
                $data['name'] = $this->session->userdata('name');
                $showLogout = true;
            } else {
                $data['email'] = $this->session->userdata('email');
                $data['name'] = $this->session->userdata('name');
                $showLogout = false;
            }
        }

        $data['roles'] = $this->role->get_all_roles();
        $data['showLogout'] = $showLogout;
        $data['main_content'] = 'auth/role_crud';
        $this->load->view('inc/template', $data);
    }

    //Landing function for assigning staff and privileges to roles
    public function add_staff_privileges($role_id) {
        $data['role_id'] = $role_id;
        $data['role_name'] = $this->role->getRoleName($role_id);
        $data['staff'] = $this->role->get_all_staff();
        $data['privileges'] = $this->role->get_privileges();
        $data['predefined'] = $this->_getPredifined();
        $data['staff_assinged'] = $this->role->get_staffassignedby_roleid($role_id);
        $data['privileges_assinged'] = $this->role->get_privilegesassignedby_roleid($role_id);
        $data['privData'] = $this->_makePrivilegesData($data['privileges'], $data['privileges_assinged']);
        // echo "<pre>"; print_r($data['privData']);
        // echo "<pre>"; print_r($data['privileges_assinged']); die();
        $data['main_content'] = 'auth/privileges_crud';
        $this->load->view('inc/template', $data);
    }

    private function _makePrivilegesData($allPrivileges, $assignedPrivileges) {
        $all = array();
        foreach ($allPrivileges as $k => $allP) {
            if(!array_key_exists($allP->p_id, $all)) {
                $all[$allP->p_id] = array();
                $all[$allP->p_id]['p_name'] = $allP->p_name;
                $all[$allP->p_id]['enable_allocation'] = $allP->enable_allocation;
                $all[$allP->p_id]['subs'] = array();
            }
            $all[$allP->p_id]['subs'][$allP->ps_id] = $allP->ps_name;
        }
        $assigned = array();
        if(!empty($assignedPrivileges)) {
            foreach ($assignedPrivileges as $key => $assP) {
                if(!array_key_exists($assP->p_id, $assigned)) {
                    $assigned[$assP->p_id] = array();
                    $assigned[$assP->p_id]['p_name'] = $assP->p_name;
                    $assigned[$assP->p_id]['subs'] = array();
                }
                $assigned[$assP->p_id]['subs'][$assP->ps_id] = $assP->ps_name;
            }
        }
        
        $privileges = array();
        foreach ($all as $pId => $pVal) {
            $privileges[$pId] = array();
            $privileges[$pId]['p_name'] = $pVal['p_name'];
            $privileges[$pId]['enable_allocation'] = $pVal['enable_allocation'];
            $privileges[$pId]['assigned'] = array();
            $privileges[$pId]['unassigned'] = array();
            if(array_key_exists($pId, $assigned)) {
                foreach ($pVal['subs'] as $spId => $spVal) {
                    if(array_key_exists($spId, $assigned[$pId]['subs'])) {
                        $privileges[$pId]['assigned'][$spId] = $spVal;
                    } else {
                        $privileges[$pId]['unassigned'][$spId] = $spVal;
                    }
                }
            } else {
                foreach ($pVal['subs'] as $spId => $spVal) {
                    $privileges[$pId]['unassigned'][$spId] = $spVal;
                }
            }
        }
        return $privileges;
    }

    private function _getPredifined() {
        $json = json_decode(file_get_contents('application/config/roles.json'));
        $names = array();
        foreach ($json as $key => $roles) {
            array_push($names, $roles->name);
        }
        return $names;
    }

    // Privileges 

    public function add_privileges() {
        $data['privileges'] = $this->role->get_privilegesAll();

        // echo "<pre>"; print_r($data['privileges']); die();

        $data['main_content'] = 'auth/privileges_add';
        $this->load->view('inc/template', $data);
    }

    public function submit_privileges() {
        $insert_privileges = $this->role->submit_privileges();
        if ($insert_privileges) {
            $this->session->set_flashdata('flashSuccess', 'Privileges created successfully');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong...');
        }
        redirect('roles/add_privileges');
    }

    public function add_sub_privileges($privilege_id) {
        $data['privileges_name'] = $this->role->get_privilegesnamebyId($privilege_id);
        $data['privileges'] = $this->role->get_privilegesAll();

        $data['main_content'] = 'auth/privileges_add';
        $this->load->view('inc/template', $data);
    }

    // Add sub privileges update by privileges id
    public function insert_sub_privileges($privilege_id) {
        $insert_sub_privileges = $this->role->submit_add_sub_privileges($privilege_id);
        if ($insert_sub_privileges) {
            $this->session->set_flashdata('flashSuccess', 'Role created successfully');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong...');
        }
        redirect('roles/index');
    }

    public function submit_role() {
        $insert_role = $this->role->submit_role();
        if ($insert_role) {
            $this->session->set_flashdata('flashSuccess', 'Role created successfully');
        } else {
            $this->session->set_flashdata('flashError', 'Something went wrong...');
        }
        redirect('roles');
    }

    public function switch_role_mode() {
        $this->role->switch_role_mode();
        $this->role->updateStaffPermissionsTable();
        echo 1;
    }

    public function submit_staff_and_privileges_to_roles($role_id) {
        // echo "<pre>"; print_r($this->input->post('privileges')); die();
        $result = $this->role->assign_staff_and_privileges_to_roles($role_id);
        if ($result['db_res']) {
            $this->role->updateStaffPermissionsTable();
            if ($result['email_res']) {
                $this->session->set_flashdata('flashSuccess', 'Submitted Successfully');
            } else {
                // Email sending failed
                $this->session->set_flashdata('flashWarning', 'Submitted Successfully. Please check the email template and add it if not present');
            }
            redirect('roles/index');
        } else {
            // Database operation failed
            $this->session->set_flashdata('flashError', 'An error occurred while processing your request.');
            redirect('roles/add_staff_privileges/' . $role_id);
        }
    }

    ///newlly added code 
    public function editprivileges($id) {
        $data['privileges'] = $this->role->get_privilegesAll();
        $data['edit_privileges'] = $this->role->edit_Privileges($id);
        $data['main_content'] = 'auth/privileges_add';
        $this->load->view('inc/template', $data);
    }

    public function update_privileges($id) {
        $updated_result = $this->role->update_Privileges($id);
        if ($updated_result) {
            $this->session->set_flashdata('flashSuccess', 'Updated Successfully');
            redirect('roles/add_privileges');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
            redirect('roles/add_privileges');
        }
    }

    public function deleteprivileges($id) {
        $delete_result = $this->role->delete_Privileges($id);
        if ($delete_result) {
            $this->session->set_flashdata('flashSuccess', 'Deleted Successfully');
            redirect('roles/add_privileges');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
            redirect('roles/add_privileges');
        }
    }

    public function getSubprivileges($id) {
        $data['edit_privileges'] = $this->role->get_SubPrivileges($id);
        //delete_sub_privileges echo "<pre>"; print_r($data['edit_privileges']); die();
        $data['main_content'] = 'auth/editsubprivileges';
        $this->load->view('inc/template', $data);
    }

    public function update_sub_privileges() {
        $name = $_POST['name'];
        $id = $_POST['id'];
        $privilege_id = $_POST['privilege_id'];
        $update_privileges = $this->role->update_SubPrivileges($id, $privilege_id, $name);
        if ($update_privileges) {

            echo "updated sub privileges successfully";
        } else {

            echo "something went wrong";
        }
    }

    public function delete_sub_privileges() {

        $id = $_POST['id'];
        $privilege_id = $_POST['privilege_id'];
        $update_privileges = $this->role->delete_SubPrivileges($id, $privilege_id);
        if ($update_privileges) {

            echo "Deleted sub privileges successfully";
        } else {

            echo "something went wrong";
        }
    }

    private function _getPrivilegesInDB() {
        $prData = $this->role->get_privilegesAll();
        $privs = $prData['prev_data'];
        $sub_privs = $prData['sub_prev_data'];
        $privileges = array();
        $sub_privileges = array();
        foreach ($privs as $k => $val) {
            $privileges[$val->pName] = $val;
            $sub_privileges[$val->pId] = array();
        }
        
        foreach ($sub_privs as $k => $val) {
            $sub_privileges[$val->privilege_id][] = array('subId' => $val->psId, 'subName' => $val->spName);
        }
        return array('privileges' => $privileges, 'sub_privileges' => $sub_privileges);
    }

    private function _getPrivilegesInJson() {
        $jsonData = json_decode(file_get_contents('application/config/privileges.json'));
        $json_privileges = array();
        foreach ($jsonData as $key => $val) {
            $json_privileges[$val->name] = $val->sub_privilege;
        }
        return $json_privileges;
    }

    private function _makeData($config, $privileges, $sub_privileges) {
        $finalData = array();
        foreach ($config as $privName => $subs) {
            $finalData[$privName] = array();
            $finalData[$privName]['privId'] = null;
            $finalData[$privName]['privName'] = $privName;
            $finalData[$privName]['in_db'] = 0;
            $finalData[$privName]['in_json'] = 1;
            $finalData[$privName]['enable_allocation'] = 1;
            $finalData[$privName]['subs'] = array();
            foreach ($subs as $sub) {
                $finalData[$privName]['subs'][$sub] = array(
                    'subId' => null,
                    'subName' => $sub,
                    'in_db' => 0,
                    'in_json' => 1
                );
            }
        }
        // echo "<pre>"; print_r($privileges);die();

        foreach ($privileges as $privName => $priv) {
            $privId =  $priv->pId;
            if(array_key_exists($privName, $finalData)) {
                $finalData[$privName]['privId'] = $privId;
                $finalData[$privName]['in_db'] = 1;
                $finalData[$privName]['enable_allocation'] = $priv->enable_allocation;
                foreach ($sub_privileges as $prId => $subs) {
                    foreach ($subs as $k => $sub) {
                        if($prId != $privId) {
                            continue;
                        }
                        if(array_key_exists($sub['subName'], $finalData[$privName]['subs'])) {
                            $finalData[$privName]['subs'][$sub['subName']]['subId'] = $sub['subId']; 
                            $finalData[$privName]['subs'][$sub['subName']]['in_db'] = 1; 
                        } else {
                            $finalData[$privName]['subs'][$sub['subName']] = array();
                            $finalData[$privName]['subs'][$sub['subName']]['subName'] = $sub['subName']; 
                            $finalData[$privName]['subs'][$sub['subName']]['subId'] = $sub['subId']; 
                            $finalData[$privName]['subs'][$sub['subName']]['in_db'] = 1;
                            $finalData[$privName]['subs'][$sub['subName']]['in_json'] = 0;
                        }
                    }
                }
            } else {
                $finalData[$privName] = array();
                $finalData[$privName]['privId'] = $privId;
                $finalData[$privName]['privName'] = $privName;
                $finalData[$privName]['in_db'] = 1;
                $finalData[$privName]['in_json'] = 0;
                $finalData[$privName]['enable_allocation'] = $priv->enable_allocation;
                $finalData[$privName]['subs'] = array();
                foreach ($sub_privileges as $prId => $subs) {
                    foreach ($subs as $k => $sub) {
                        if($prId != $privId) {
                            continue;
                        }
                        $finalData[$privName]['subs'][$sub['subName']] = array();
                        $finalData[$privName]['subs'][$sub['subName']]['subName'] = $sub['subName']; 
                        $finalData[$privName]['subs'][$sub['subName']]['subId'] = $sub['subId']; 
                        $finalData[$privName]['subs'][$sub['subName']]['in_db'] = 1;
                        $finalData[$privName]['subs'][$sub['subName']]['in_json'] = 0;
                    }
                }
            }
        }
        // echo "<pre>"; print_r($finalData);die();
        return $finalData;
    }

    private function _compareJsonAndDbPrivileges() {
        $dbPriv = $this->_getPrivilegesInDB();
        // echo '<pre>'; print_r($dbPriv); die();
        $configPriv = $this->_getPrivilegesInJson();
        $privileges = $dbPriv['privileges'];
        $sub_privileges = $dbPriv['sub_privileges'];
        $finalData = $this->_makeData($configPriv, $privileges, $sub_privileges);
        return $finalData;
    }

    public function privileges() {
        $data['privileges'] = $this->_compareJsonAndDbPrivileges();
        // echo "<pre>"; print_r($data); die();
        $data['main_content'] = 'roles/index';
        $this->load->view('inc/template', $data);
    }

    public function addPrivilege() {
        $name = trim($_POST['name']);
        $status = $this->role->addPrivilege($name);
        echo $status;
    }

    public function addSubPrivilege() {
        $sub_name = trim($_POST['sub_name']);
        $privilege_id = $_POST['privilege_id'];
        $status = $this->role->addSubPrivilege($sub_name, $privilege_id);
        echo $status;
    }

    public function removeSubPrivilege() {
        $sub_priv_id = $_POST['sub_priv_id'];
        $status = $this->role->removeSubPrivilege($sub_priv_id);
        echo $status;
    }

    public function edit_privilege_json() {
        $data['json'] = file_get_contents('application/config/privileges.json');
        $data['main_content'] = 'roles/privilege_json';
        $this->load->view('inc/template', $data);
    }

    public function update_privilege_json() {
        $json = $this->input->post('privilege_json');
        $status = file_put_contents('application/config/privileges.json', $json);
        if ($status) {
            $this->session->set_flashdata('flashSuccess', 'Updated Successfully');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
        }
        redirect('roles/privileges');
    }

    private function _makeDeafultPrivs($jsonData) {
        $subIds = [];
        foreach ($jsonData as $jd) {
            $subs = $this->role->getPrivilegeIds($jd->name, $jd->subs);
            foreach ($subs as $sub) {
                array_push($subIds, $sub);
            }
        }
        return $subIds;
    }

    public function getDeafultPrivileges() {
        $roleName = $_POST['roleName'];
        $json = json_decode(file_get_contents('application/config/roles.json'));
        $subIds = array();
        $found = 0;
        foreach ($json as $key => $roles) {
            if($roles->name == $roleName) {
                $found = 1;
                $subIds = $this->_makeDeafultPrivs($roles->privilege);
                break;
            }
        }
        echo json_encode(array('found' => $found, 'subs'=> $subIds));
    }

    public function changeAllocationStatus() {
        $privilege_id = $_POST['privilege_id'];
        $status = $_POST['status'];
        echo $this->role->changeAllocationStatus($privilege_id, $status);
    }

    public function view_single_staff_privileges(){
        $data['staff_list'] = $this->role->get_staff_list();
        $data['main_content'] = 'auth/view_single_staff_privileges';
        $this->load->view('inc/template', $data);
    }

    public function viewRolesByStaff(){
        $staff_id = $_POST['staff_id'];
        $data = $this->role->viewRolesByStaff($staff_id);
        

        echo json_encode($data);
    }

    public function get_role_privileges(){
        $staff_id = $_POST['staff_id'];
        $role_id = $_POST['role_id'];

        $data = $this->role->get_role_privileges($staff_id, $role_id);
        //echo "<pre>";print_r($data);die();
        echo json_encode($data);
    }

    public function view_role_privileges(){
        $data['privilege_list'] = $this->role->get_privilege_list();
        $data['main_content'] = 'auth/view_role_privileges';
        $this->load->view('inc/template', $data);
    }

    public function role_history_report(){
        $data['roles'] = $this->role->get_roles();
        $data['main_content']    = 'auth/role_history_report';
        $this->load->view('inc/template', $data);
    }

    public function get_role_history(){
        $role_id = $_POST['role_id'];
        $from_date = $_POST['from_date'];
        $to_date = $_POST['to_date'];
        $data = $this->role->get_role_history($role_id, $from_date, $to_date);
        echo json_encode($data);
    }

    public function viewRolesByprivilege(){
        $privilege_id = $_POST['privilege_id'];
        $sub_privilege_id = $_POST['sub_privilege_id'];
        $data = $this->role->viewRolesByprivilege($privilege_id, $sub_privilege_id);
        echo json_encode($data);
    }

    public function get_sub_previleges_list(){
        $privilege_id = $_POST['privilege_id'];
        $data = $this->role->get_sub_previleges_list($privilege_id);
        echo json_encode($data);
    }

    public function unassign_staff_from_role(){
        $staff_id = $_POST['staff_id'];
        $role_id = $_POST['role_id'];

        $data = $this->role->unassign_staff_from_role($staff_id, $role_id);
        if ($data) {
            $this->role->updateStaffPermissionsTable();
            $this->session->set_flashdata('flashSuccess', 'Deallocated Successfully');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
        }

        echo json_encode($data);
    }
}
