<?php
class Parent_activation_model extends CI_Model {

    private $yearId;
    private $current_branch;
    public function __construct() {
        parent::__construct();
        $this->yearId =  $this->acad_year->getAcadYearId();
        $this->current_branch = $this->authorization->getCurrentBranch();
    }


    public function getstudentDetails($section_id) {

        $this->db->select("s.id as std_id,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as studentName,c.class_name,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName,cs.section_name as sectionName, cs.id as csId,p.mobile_no as mobile,p.id as pid, u.active as Active, u.id as userId, sr.relation_type, u.username, s.dob, s.admission_no, u.loggedin_atleast_once, p.email, a.old_user_id as oldUid, ay.acad_year")
        ->from('student_admission s')
        ->join('student_year sy', 'sy.student_admission_id=s.id');
        if($section_id != -1){
            $this->db->where('sy.class_section_id', $section_id);
        }
        $this->db->where('s.admission_status', '2');
        $this->db->join('academic_year ay', 's.admission_acad_year_id=ay.id');
        $this->db->join('student_relation sr','s.id=sr.std_id');
        $this->db->join('parent p','sr.relation_id=p.id');
        $this->db->join('avatar a','a.stakeholder_id=p.id');
        $this->db->join('users u','u.id=a.user_id');
        $this->db->where('a.avatar_type',2);
        $this->db->join('class c','sy.class_id=c.id');
        $this->db->join('class_section cs', 'sy.class_section_id=cs.id');
        $this->db->order_by('cs.id, s.first_name');
        return $this->db->get()->result();
    }

    public function getPreviewData(){
        $pids = $_POST['pids'];
        $this->db->select("s.id as std_id,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as studentName,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName,p.mobile_no as mobile,p.id as pid,sr.relation_type, p.email, u.username, u.id as user_id");
        $this->db->from('parent p');
        $this->db->join('student_relation sr','p.id=sr.relation_id');
        $this->db->join('student_admission s','s.id=sr.std_id');
        $this->db->join('avatar a','a.stakeholder_id=p.id');
        $this->db->join('users u','u.id=a.user_id');
        $this->db->where('s.admission_status', '2');
        $this->db->where('a.avatar_type', '2');
        $this->db->where_in('p.id', $pids);
        return $this->db->get()->result();
    }

    public function getstudentDetailsByName($std_name) {
        $this->db->select("s.id as std_id,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as studentName,cs.class_name,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName,cs.section_name as sectionName, cs.id as csId,p.mobile_no as mobile,p.id as pid, u.active as Active, u.id as userId, sr.relation_type, u.username, s.dob, s.admission_no, u.loggedin_atleast_once, p.email, a.old_user_id as oldUid, ay.acad_year")
        ->from('student_admission s')
        ->join('student_year sy', 'sy.student_admission_id=s.id');
        $this->db->join('academic_year ay', 's.admission_acad_year_id=ay.id');
        $this->db->join('student_relation sr','s.id=sr.std_id');
        $this->db->join('parent p','sr.relation_id=p.id');
        $this->db->join('avatar a','a.stakeholder_id=p.id');
        $this->db->join('users u','u.id=a.user_id');
        $this->db->join('class_section cs', 'sy.class_section_id=cs.id');
        $this->db->where('a.avatar_type',2);
        $this->db->where('s.admission_status', '2');
        $this->db->like('s.first_name', $std_name,'both');
        $this->db->order_by('s.admission_acad_year_id, cs.id, s.first_name');
        return $this->db->get()->result();
    }

    public function get_provision_student_list_by_filter($classSectionId, $std_name){
        
        $this->db_readonly->select("sa.id as std_id")
        ->from('student_admission sa')
        ->join('student_year sy', 'sy.student_admission_id=sa.id');
        if (empty($std_name) || $classSectionId != 0) {
            if($classSectionId == 0) {
                return array();
            }
            if($classSectionId != -1){
                list($section_id, $class_id) = explode("_", $classSectionId); 
            }
            if($classSectionId != -1){
                $this->db_readonly->where('sy.class_section_id', $section_id);
            }
        }
        $this->db_readonly->where('sa.admission_status', '2');
        $this->db_readonly->where('sy.promotion_status!=', '4');
        $this->db_readonly->where('sy.promotion_status!=', '5');
        $this->db_readonly->where('sy.acad_year_id', $this->acad_year->getAcadYearId());
        if($std_name) {
            $this->db_readonly->like('sa.first_name', $std_name);
        }
        $this->db_readonly->join('class c','sy.class_id=c.id');
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        $this->db_readonly->join('class_section cs', 'sy.class_section_id=cs.id');
        $this->db_readonly->order_by('cs.id,sa.first_name');
        $result =  $this->db_readonly->get()->result();
        $stdIds = [];
        foreach ($result as $key => $res) {
            array_push($stdIds, $res->std_id);
        }
        return $stdIds;

    }

    public function getProvisionStudentDetails($student_ids) {
        $result =  $this->db_readonly->select("s.id as std_id,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as studentName,c.class_name,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName,cs.section_name as sectionName, cs.id as csId,p.mobile_no as mobile,p.id as pid, u.active as Active, u.id as userId, sr.relation_type, u.username, up.attempts, s.dob, s.admission_no, cs.class_teacher_id, CONCAT(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staffName, up.code, u.loggedin_atleast_once, p.email, a.old_user_id as oldUid, u.token")
        ->select("CASE 
            WHEN sy.admission_type = '1' THEN 'Re-Admission' 
            WHEN sy.admission_type = '2' THEN 'New Admission' 
            ELSE 'Other' 
        END AS admissionStatus", false)
        ->from('student_admission s')
        ->where_in('s.id',$student_ids)
        ->join('student_year sy', 'sy.student_admission_id=s.id')
        ->where('sy.acad_year_id', $this->acad_year->getAcadYearId())
        ->join('student_relation sr','s.id=sr.std_id')
        ->join('parent p','sr.relation_id=p.id')
        ->join('avatar a','a.stakeholder_id=p.id')
        ->join('users u','u.id=a.user_id')
        ->join('user_prov_login_att up','up.parent_id=p.id','left')
        ->where('a.avatar_type',2)
        ->join('class c','sy.class_id=c.id')
        ->join('class_section cs', 'sy.class_section_id=cs.id')
        ->join('staff_master sm', 'sm.id=cs.class_teacher_id','left')
        ->group_by('p.id')
        ->get()->result();
        
        foreach($result as $key => $val){
            $val->siblings = array();
            if(!empty($val->oldUid) && $val->userId != $val->oldUid){
                $val->siblings = $this->get_sibling_names($val->oldUid,$val->studentName); 
            }
        }
        return $result;
    }

    private function get_sibling_names($oldUid,$studentName){
        $parentAvatarSQL = "select std.id as sa_id, a.user_id, a.old_user_id, u.username, concat(ifnull(p.first_name,''), ' ' ,ifnull(p.last_name,'')) as pName, concat(ifnull(std.first_name,''), ' ' ,ifnull(std.last_name,'')) as sName, concat(cs.class_name, cs.section_name) as csName from avatar a 
        join parent p on p.id=a.stakeholder_id 
        join users u on a.user_id=u.id 
        join student_admission std on p.student_id=std.id
        join student_year sy on std.id=sy.student_admission_id and sy.acad_year_id=$this->yearId
        join class_section cs on cs.id=sy.class_section_id where a.old_user_id =$oldUid and avatar_type=2 order by a.user_id";

        $pAvatarResult = $this->db->query($parentAvatarSQL)->result();
        $mergeAvatarArr = array();

        foreach ($pAvatarResult as $pAvatar){
            // if($pAvatar->sName != $studentName){
                $mergeAvatarArr[] = array(
                    'stdName' => $pAvatar->sName,
                    'aSection' => $pAvatar->csName,
                    'parent'=>$pAvatar->pName
                );
            // }
        }
            return $mergeAvatarArr;
    }

    public function deactivate_provision_credentials_by_user_id($userId){
        $this->db->where_in('id', $userId);
        return $this->db->update('users', array('active' => 0, 'loggedin_atleast_once' => 0,'token'=>null));
    }

    public function getPreviewCredentialsData($parentId){
        $this->db_readonly->select("s.id as std_id,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as studentName,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName,p.mobile_no as mobile,p.id as pid,sr.relation_type, p.email, u.username, u.id as user_id");
        $this->db_readonly->from('parent p');
        $this->db_readonly->join('student_relation sr','p.id=sr.relation_id');
        $this->db_readonly->join('student_admission s','s.id=sr.std_id');
        $this->db_readonly->join('avatar a','a.stakeholder_id=p.id');
        $this->db_readonly->join('users u','u.id=a.user_id');
        $this->db_readonly->where('s.admission_status', '2');
        $this->db_readonly->where('a.avatar_type', '2');
        $this->db_readonly->where('p.id', $parentId);
        return $this->db_readonly->get()->row();
    }

    public function getPreviewCredentialsData_admission_flow($parentId){
        $this->db_readonly->select("s.id as std_id,CONCAT(ifnull(s.first_name,''),' ',ifnull(s.last_name,'')) as studentName,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as parentName,p.mobile_no as mobile,p.id as pid,sr.relation_type, p.email, u.username, u.id as user_id");
        $this->db_readonly->from('parent p');
        $this->db_readonly->join('student_relation sr','p.id=sr.relation_id');
        $this->db_readonly->join('student_admission s','s.id=sr.std_id');
        $this->db_readonly->join('avatar a','a.stakeholder_id=p.id');
        $this->db_readonly->join('users u','u.id=a.user_id');
        $this->db_readonly->where('a.avatar_type', '2');
        $this->db_readonly->where('p.id', $parentId);
        return $this->db_readonly->get()->row();
    }

}