<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard');?>">Fees Dashboard</a></li>
  <li>Fees Balance / SMS Report</li>
</ul>
<hr>
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-5">
          <div class="d-flex justify-content-between" style="width:100%;">
            <h3 class="card-title panel_title_new_style_staff">
              <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard'); ?>">
                <span class="fa fa-arrow-left"></span>
              </a> 
              Fees <PERSON>lance / SMS Report
            </h3>
          </div>
        </div>

        <div class="col-md-5">
          <div class="d-flex justify-content-end" style="width: 100%; padding: 10px;">
            <label class="checkbox-inline d-flex align-items-center" style="margin-right: 20px;">
              <input class="hover-checkbox" style="width: 18px; height: 18px;" type="checkbox" name="installment_search" id="installment_search">
              <span style="font-size: 16px; margin-left: 8px;">Show Installments</span>
            </label>
            <label class="checkbox-inline d-flex align-items-center">
              <input class="hover-checkbox" style="width: 18px; height: 18px;" type="checkbox" value="1" name="show_over_due" id="show_over_due">
              <span style="font-size: 16px; margin-left: 8px;">Show Overdue only</span>
            </label>
          </div>
        </div>
        <style>
          .hover-checkbox:hover {
            transform: scale(1.1);
            transition: transform 0.2s ease;
          }
        </style>

        <div class="col-md-2">
          <?php 
            $arr = array();
            foreach ($admissionStatusArr as $key => $value) { 
                $arr[$key] = $value; 
            }
          ?>
          <select name="admission_status[]" id="admission_status" multiple title="Select Status" class="form-control select">
              <!-- <option value="">Select Section</option> -->
              <?php foreach ($arr as $key => $class) { ?>
              <option <?php if(in_array($key, $adSelected)) echo 'selected' ?> value="<?= $key ?>"><?php echo $class;?></option>
              <?php } ?>
          </select>
        </div>
      </div>
    </div>

    <div class="card-body pt-1">
      
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-2 form-group" id="searchBYstudentId">
            <p>Search By Student Name</p>
            <input id="student_name_fees" autocomplete="off" placeholder="Search by Student Name" class="form-control input-md" name="student_name">
            <input type="hidden" id="stakeholder_id" value="0">
          </div>

          <div class="col-md-3 form-group" id="multiBlueprintSelect">
            <p>Select Fee Type</p>
            <select class="form-control multiselect select" multiple title='All' id="blueprint_type" name="fee_type">
              <?php foreach ($fee_blueprints as $key => $val) { ?>
                <option <?php if($fee_type == $val->id) echo "selected"; ?> value="<?= $val->id ?>"><?php echo $val->name?></option>
              <?php } ?>
            </select>
          </div>

          <div class="col-md-2 form-group" style="display: none;" id="blueprintSelect">
            <p>Select Fee Type</p>
            <select class="form-control changeFeeType" id="blueprint_type" name="fee_type">
              <option value="">Select Blueprint</option>
              <?php foreach ($fee_blueprints as $key => $val) { ?>
                <option value="<?= $val->id ?>"><?php echo $val->name?></option>
              <?php } ?>
            </select>
          </div>

          <div class="col-md-2 form-group" id="installmentType" style="display: none;">
            <p>Select Installments Type</p>
            <select class="form-control" name="installment_type" id="installment_type"></select>
          </div>
        
          <div class="col-md-2 form-group" id="installment" style="display: none">
            <p>Select Installments</p>
            <select class="form-control" multiple title='All' name="installment"  id="installmentId"></select>
          </div>

          <?php if ($this->settings->getSetting('puc_combination')) { ?>
            <div class="col-md-2 form-group">
              <p>Select Combination</p>
              <?php
                $combinationArry = array();
                $combinationArry[0] = 'Select Combination';
                foreach ($combinationList as $key => $comb) {
                    $combinationArry[$comb->combination] = strtoupper($comb->combination);
                }
                echo form_dropdown("combination", $combinationArry, set_value("combination"), "id='combination' class='form-control'");
              ?>
            </div>
          <?php } ?>

          <?php if ($this->settings->getSetting('fees_balance_report_show_rte_filter')) { ?>
            <div class="col-md-2 form-group">
              <p>Select RTE Type</p>
              <?php
                $rte_nrte = array();
                $rte_nrte[0] = 'Select RTE Type';
                foreach ($rteType as $key => $rn) {
                    $rte_nrte[$key] = $rn;
                }
                echo form_dropdown("rte_nrte", $rte_nrte, set_value("rte_nrte"), "id='rte_nrteId' class='form-control'");
              ?>
            </div>
          <?php } ?>

          <?php if ($this->settings->getSetting('fees_balance_show_staff_kids_filter')) { ?>
            <div class="col-md-2 form-group">
              <p>Select Staff Kids</p>
              <select class="form-control" name="staff_kids" id="staff_kid">
                <option value="">All</option>
                <option value="0">Exclude Staff Kids</option>
                <option value="1">Staff Kids</option>
              </select>
            </div>
          <?php } ?>

          <?php if ($this->settings->getSetting('fees_balance_report_show_partially_filter')) { ?>
            <div class="col-md-2 form-group">
              <p>Payment Options</p>
              <select  name="payment_status"  id='payment_status' class='form-control'>
                <option value="">Payment Option</option>
                <option value="PARTIAL">Partial Payment</option>
                <option value="NOT_STARTED">Balance</option>
              </select>
            </div>
          <?php } ?>

          <div class="col-md-3 form-group">
            <p>Serach by Class </p>
            <?php 
                $array = array();
                // $array[0] = 'Select Classes';
                foreach ($classes as $key => $class) {
                  $array[$class->classId] = $class->className; 
                }
              echo form_dropdown("class_name[]", $array, set_value("class_name"), "id='classId' multiple title='All' class='form-control classId select '");
            ?>
          </div>

           <div class="col-md-2 form-group">
            <p>Select Class/Sections</p>
              <select class="form-control select" multiple title='All' id="classSectionId" name="classSectionId" data-actions-box="true" data-selected-text-format="count > 3">
                <option value="">Select Class/Section</option>
              </select>
            </div>

            <?php if ($this->settings->getSetting('fee_report_donor_filter')) : ?>
              <div class="col-md-3 form-group">
                <p>Select Donors</p>
                  <?php 
                  $array = array();
                  $array[0] = 'Select Donors';
                  foreach ($donors as $key => $donor) {
                      $array['Null'] = 'Non Donors';
                      $array[$donor->donor] = ucfirst($donor->donor);
                  }
                  echo form_dropdown("donors", $array, set_value("donors"), "id='donorsId' class='form-control'");
                  ?>
              </div>
            <?php endif ?>

          <div class="col-md-2 form-group">
            <p></p>
            <br>
            <input type="button" value="Get Report" onclick="get_balance_report()" name="search" id="search" value="Get Report" id="getReport" class="btn btn-primary">
          </div>
        </div>
        <div class="row" style="margin: 0px">
          <div class="form-group col-md-4">
            <label class="control-label mr-3">Report </label>
            <label class="radio-inline" for="type-1">
              <input type="radio" name="report_type" id="type-1" value="1" checked="">Detailed
            </label>
            <label class="radio-inline" for="type-2">
              <input type="radio" name="report_type" id="type-2" value="2">Summary
            </label>
          </div>

          <?php if ($this->authorization->isAuthorized('FEESV2.STUDENT_WISE_PREDEFINED_FILTERS')) { ?>
            <div class="col-md-8" style="float: right;">
              <div class="form-group">
                <label class="col-md-4 control-label" style="text-align: right;margin-top:5px">Saved Reports</label>
                <div class="col-md-4">
                  <select name="" id="filter_types" class="form-control col-12" onchange="selectFilters()">
                    <option value="">Select Report</option>
                  </select>
                </div>

                <?php if ($this->authorization->isAuthorized('FEESV2.STUDENT_WISE_PREDEFINED_FILTERS')) { ?>
                  <div class="dt-buttons col-md-4" style="text-align:left; display: flex; gap: 10px;">
                    <input type="button" name="reload" id="reload_filter" class="btn btn-info col-4" style="border-radius: 8px; display: inline-block; margin-right:2px; padding: 4px 14px;" value="Reload" onclick="selectFilters()">
                    <input type="button" name="save" id="save_filter" class="btn btn-info col-4" style="border-radius: 8px; display: inline-block; margin-right:2px; padding: 4px 14px;" value="Save">
                    <input type="button" name="update" id="update_filter" class="btn btn-info col-4" style="border-radius: 8px; display: inline-block; margin-right:2px; padding: 4px 14px;" value="Update">
                  </div>

                  <style>
                    .btn-info {
                      color: #fff !important;
                      background-color: #17a2b8 !important;
                      border-color: #17a2b8 !important;
                      padding: 4px 14px !important;

                      /* Half-width Bootbox styling */
                      .half-width-box .modal-dialog {
                        max-width: 400px;
                        margin: 1.75rem auto;
                      }

                      /* PNotify box center */
                      .custom-pnotify.half-width-notify {
                        width: 400px !important;
                        left: 50% !important;
                        margin-left: -200px !important;
                        text-align: center;
                      }

                    }
                  </style>
                  <script>
                    $(document).ready(function() {
                      // get_balance_report();
                      get_predefined_filters();

                      $('#save_filter').on('click', function() {
                        saveFilter();
                      });

                      $('#update_filter').on('click', function() {
                        updateFilter();
                      });

                      $('#clear_filter').on('click', function() {
                        clearFilters();
                      });

                      $('.select').on('keydown', function(e) {
                        if (e.key === 'Escape') {
                          $(this).closest('.dropdown').find('.dropdown-toggle').dropdown('hide');
                        }

                        if (e.key === 'Enter') {
                          $(this).closest('.dropdown').find('.dropdown-toggle').dropdown('hide');
                        }

                        if (e.key === ' ' && !$(this).is(':focus')) {
                          e.preventDefault();
                        }
                      });

                      $(document).on('click', function(event) {
                            var $target = $(event.target);
                            if (!$target.closest('.bootstrap-select').length && $('.bootstrap-select').hasClass('open')) {
                                $('.bootstrap-select').removeClass('open show'); 
                                $('.dropdown-menu').removeClass('show'); 
                            }
                        });
                    });

                    function collectFilterData() {
                      let table, visibleCols = [];

                      if ($('#fee_summary_data').length && $.fn.DataTable.isDataTable('#fee_summary_data')) {
                        table = $('#fee_summary_data').DataTable();
                      } else if ($('#fee_summary_data_summary').length && $.fn.DataTable.isDataTable('#fee_summary_data_summary')) {
                        table = $('#fee_summary_data_summary').DataTable();
                      }

                      if (table) {
                        table.columns().every(function(index) {
                          if (this.visible()) {
                            visibleCols.push(index);
                          }
                        });
                      }

                      return {
                        title: $('#filter_types option:selected').text().trim(),
                        installment_search: $('#installment_search').is(':checked'),
                        show_over_due: $('#show_over_due').is(':checked'),
                        admission_status: $('#admission_status').val(),
                        student_name_fees: $('#student_name_fees').val(),
                        blueprint_type: $('#blueprint_type').val(),
                        installment_type: $('#installment_type').val(),
                        installmentId: $('#installmentId').val(),
                        combination: $('#combination').val(),
                        rte_nrteId: $('#rte_nrteId').val(),
                        staff_kid: $('#staff_kid').val(),
                        payment_status: $('#payment_status').val(),
                        classId: $('#classId').val(),
                        classSectionId: $('#classSectionId').val(),
                        donorsId: $('#donorsId').val(),
                        report_type: $('input[name="report_type"]:checked').val(),
                        column_visibility: JSON.stringify(visibleCols)
                      };
                    }

                    function saveFilter() {
                      bootbox.prompt({
                        inputType: 'text',
                        placeholder: 'Enter the Title name',
                        title: "Save filters",
                        className: 'half-width-box',
                        buttons: {
                          confirm: {
                            label: 'Yes',
                            className: 'btn-success'
                          },
                          cancel: {
                            label: 'No',
                            className: 'btn-danger'
                          }
                        },
                        callback: function(remarks) {
                          if (remarks === null) return;

                          $('.bootbox .error-message').remove();
                          remarks = remarks.trim();

                          if (!remarks) {
                            new PNotify({
                              title: 'Missing Title',
                              text: 'Please enter a name to save the filter.',
                              type: 'error',
                              addclass: 'custom-pnotify half-width-notify',
                              cornerclass: '',
                              animate: {
                                animate: true,
                                in_class: 'fadeInRight',
                                out_class: 'fadeOutRight'
                              },
                              styling: 'bootstrap3',
                              delay: 3000
                            });
                            return false;
                          }

                          if (remarks.length < 5 || remarks.length > 50) {
                            setTimeout(() => {
                              $('.bootbox-input').after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">Title must be between 5 and 50 characters.</div>`);
                            }, 10);
                            return false;
                          }

                          let duplicate = false;
                          $('#filter_types option').each(function() {
                            if ($(this).text().trim().toLowerCase() === remarks.toLowerCase()) {
                              duplicate = true;
                              return false;
                            }
                          });

                          if (duplicate) {
                            setTimeout(() => {
                              $('.bootbox-input').after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">A filter with this name already exists.</div>`);
                            }, 10);
                            return false;
                          }

                          const exists = $('#filter_types option').filter(function() {
                            return $(this).text().trim().toLowerCase() === remarks.toLowerCase();
                          }).length > 0;

                          if (exists) {
                            bootbox.alert({
                              title: "Duplicate Filter",
                              message: `A filter with the title "<strong>${remarks}</strong>" already exists.`,
                              className: 'half-width-box'
                            });

                            setTimeout(() => {
                              $('.bootbox .modal-dialog').css({
                                'max-width': '400px',
                                'margin': '1.75rem auto'
                              });
                            }, 10);

                            return;
                          }

                          let isInstallmentChecked = $('#installment_search').is(':checked');
                          let table, visibleCols;

                          // Check which table is being used (fee_summary_data or fee_summary_data_summary)
                          if ($('#fee_summary_data').length) {
                            table = $('#fee_summary_data').DataTable();
                          } else if ($('#fee_summary_data_summary').length) {
                            table = $('#fee_summary_data_summary').DataTable();
                          }

                          // Collect visible columns for the appropriate table
                          visibleCols = [];
                          if (table && $.fn.dataTable.isDataTable(table.table().node())) {
                              table.columns().every(function(index) {
                                if (this.visible()) {
                                  visibleCols.push(index);
                                }
                              });
                            }

                            if (visibleCols.length === 0) {
                                new PNotify({
                                  title: 'Action Required',
                                  text: 'Please click the "Get Report" button first before saving a filter.',
                                  type: 'warning',
                                  addclass: 'custom-pnotify half-width-notify',
                                  delay: 3000
                                });
                                return false;
                              }

                          let filterData = {
                            title: remarks,
                            installment_search: isInstallmentChecked,
                            show_over_due: $('#show_over_due').is(':checked'),
                            admission_status: $('#admission_status').val(),
                            student_name_fees: $('#student_name_fees').val(),
                            stakeholder_id: $('#stakeholder_id').val(),
                            combination: $('#combination').val(),
                            rte_nrteId: $('#rte_nrteId').val(),
                            staff_kid: $('#staff_kid').val(),
                            payment_status: $('#payment_status').val(),
                            classId: $('#classId').val(),
                            classSectionId: $('#classSectionId').val(),
                            donorsId: $('#donorsId').val(),
                            report_type: $('input[name="report_type"]:checked').val(),
                            column_visibility: visibleCols.length > 0 ? JSON.stringify(visibleCols) : null,
                          };

                          if (isInstallmentChecked) {
                            filterData.installment_type = $('#installment_type').val();
                            filterData.installmentId = $('#installmentId').val();
                            filterData.blueprint_type = $('#blueprint_type').val();
                          } else {
                            filterData.blueprint_type = $('#blueprint_type').val();
                          }
                          
                          $.ajax({
                            url: '<?php echo site_url('feesv2/reports/save_filters2'); ?>',
                            type: 'POST',
                            data: filterData,
                            success: function(data) {
                              if (data) {
                                let lastId = 0;
                                $('#filter_types option').each(function() {
                                  const val = parseInt($(this).val());
                                  if (!isNaN(val) && val > lastId) lastId = val;
                                });

                                const newId = lastId + 1;

                                $('#filter_types').append(
                                  $('<option>', {
                                    value: newId,
                                    text: remarks
                                  })
                                );

                                $('#filter_types').val(newId).trigger('change');

                                new PNotify({
                                  title: 'Success',
                                  text: 'Filter Saved Successfully',
                                  type: 'success',
                                  addclass: 'custom-pnotify half-width-notify'
                                });
                              } else {
                                new PNotify({
                                  title: 'Error',
                                  text: 'Something went wrong',
                                  type: 'error',
                                  addclass: 'custom-pnotify half-width-notify'
                                });
                              }
                            }
                          });
                        }
                      });

                      // Apply modal centering and width on open
                      setTimeout(() => {
                        $('.bootbox .modal-dialog').css({
                          'max-width': '400px',
                          'margin': '1.75rem auto'
                        });

                        setTimeout(() => {
                          // Adjust modal dialog styles
                          $('.bootbox .modal-dialog').css({
                            'max-width': '400px',
                            'margin': '1.75rem auto'
                          });

                          // Input validation for character length
                          $('.bootbox-input').on('input', function() {
                            const inputVal = $(this).val();
                            if (inputVal.length < 5 || inputVal.length > 50) {
                              $(this).val(inputVal.slice(0, 50)); // Truncate input to 50 characters
                              $('.bootbox .error-message').remove(); // Remove previous error message
                              $(this).after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">Title must be between 5 and 50 characters.</div>`);
                            } else {
                              $('.bootbox .error-message').remove(); // Remove error message when valid length
                            }
                          });
                        }, 200);
                      }, 10);
                    }

                    function updateFilter() {

                      const selectedFilterId = $('#filter_types').val();

                      if (!selectedFilterId) {
                        bootbox.alert({
                          title: "No Filter Selected",
                          message: "Please select a filter to update.",
                          className: "half-width-box",
                          buttons: {
                            ok: {
                              label: 'OK',
                              className: 'btn-primary'
                            }
                          }
                        });

                        // Center the alert modal
                        setTimeout(() => {
                          $('.bootbox .modal-dialog').css({
                            // 'max-width': '400px',
                            'margin': '1.75rem auto'
                          });
                        }, 10);

                        return; // stop the update function
                      }

                      let filterData = collectFilterData();
                      filterData.filter_types_id = $('#filter_types').val();
                      filterData.title = $('#filter_types option:selected').text().trim();
                      filterData.stakeholder_id = $('#stakeholder_id').val();

                      bootbox.confirm({
                        title: "Update Filters",
                        message: 'Are you sure you want to update the filter?',
                        className: "half-width-box",
                        buttons: {
                          confirm: {
                            label: 'Yes',
                            className: 'btn-success'
                          },
                          cancel: {
                            label: 'No',
                            className: 'btn-danger'
                          }
                        },
                        callback: function(result) {
                          if (result) {
                            $.ajax({
                              url: '<?php echo site_url('feesv2/reports/update_filters2'); ?>',
                              type: 'POST',
                              data: filterData,
                              complete: function() {
                                $.when(get_predefined_filters()).done(function() {
                                  if (
                                    $('#filter_types option[value="' + filterData.filter_types_id + '"]').length === 0
                                  ) {
                                    $('#filter_types').append(
                                      $('<option>', {
                                        value: filterData.filter_types_id,
                                        text: filterData.title
                                      })
                                    );
                                  }

                                  $('#filter_types').val(filterData.filter_types_id);
                                  selectFilters();

                                  new PNotify({
                                    title: 'Success',
                                    text: 'Filter updated successfully.',
                                    type: 'success',
                                    addclass: 'custom-pnotify half-width-notify'
                                  });
                                });
                              }
                            });
                          }
                        }
                      });

                      // Force modal width and position after render
                      setTimeout(() => {
                        $('.bootbox .modal-dialog').css({
                          'max-width': '400px',
                          'margin': '1.75rem auto'
                        });
                      }, 10);
                    }

                    function get_predefined_filters() {
                      return $.ajax({
                        url: '<?php echo site_url('feesv2/reports/get_predefined_filters2'); ?>',
                        type: 'POST',
                        success: function(data) {
                          try {
                            var res_data = JSON.parse(data);

                            if (Array.isArray(res_data) && res_data.length > 0) {
                              var html = '<option value="">Select</option>';
                              res_data.forEach(filter => {
                                html += `<option value="${filter.id}">${filter.title}</option>`;
                              });

                              $('#filter_types').html(html);
                            } else {
                              console.warn("No predefined filters found.");
                              $('#filter_types').html('<option value="">No Filters Available</option>');
                            }
                          } catch (error) {
                            console.error("Error parsing response:", error);
                            $('#filter_types').html('<option value="">Error Loading Filters</option>');
                          }
                        },
                        error: function(xhr, status, error) {
                          console.error("AJAX Error:", error);
                          $('#filter_types').html('<option value="">Error Fetching Filters</option>');
                        }
                      });
                    }


                    function selectFilters() {
                      const filterId = $('#filter_types').val();
                      if (!filterId || filterId.trim() === '') {
                        $('#reload_filter').show();
                        // clear_filters();
                        return;
                      }

                      $('#reload_filter').show();

                      $.ajax({
                        url: '<?php echo site_url('feesv2/reports/get_predefined_filters_by_id2'); ?>',
                        type: 'POST',
                        data: { filter_id: filterId },
                        dataType: 'json',
                        success: function(response) {
                          if (response && response.success && response.filters_selected) {
                            const filters = response.filters_selected;

                            let visibleCols = [];
                            if (filters.column_visibility) {
                              try {
                                visibleCols = JSON.parse(filters.column_visibility).map(Number);
                                if (!Array.isArray(visibleCols)) visibleCols = [];
                              } catch (e) {
                                console.error('Invalid column_visibility format:', filters.column_visibility);
                                visibleCols = [];
                              }
                            }

                            const updateSelect = (selector, values) => {
                              if (values !== undefined && values !== null) {
                                if (typeof values === 'string') values = values.split(',');
                                $(selector).val(values);
                                $(selector).selectpicker?.('refresh');
                              }
                            };

                            $('#installment_search').prop('checked', filters.installment_search === "true").trigger('change');
                            $('#show_over_due').prop('checked', filters.show_over_due === "true");

                            updateSelect('#blueprint_type', filters.blueprint_type);
                            updateSelect('#fee_type', filters.fee_type);
                            updateSelect('#admission_status', filters.admission_status);
                            updateSelect('#combination', filters.combination);
                            updateSelect('#rte_nrteId', filters.rte_nrteId);
                            updateSelect('#staff_kid', filters.staff_kid);
                            updateSelect('#payment_status', filters.payment_status);
                            updateSelect('#donorsId', filters.donorsId);
                            updateSelect('#paymentModes', filters.paymentModes);
                            updateSelect('#admission_type', filters.admission_type);
                            

                            $('#student_name_fees').val(filters.student_name_fees);
                            $('#stakeholder_id').val(filters.stakeholder_id || '0');

                            if (filters.report_type) {
                              $(`input[name="report_type"][value="${filters.report_type}"]`).prop('checked', true);
                            }

                            const classSectionFlow = new Promise((resolve) => {
                              updateSelect('#classId', filters.classId);
                              $.ajax({
                                url: '<?php echo site_url('feesv2/reports_v2/get_class_section_by_fees_selection_class') ?>',
                                type: 'post',
                                data: { feeclass: filters.classId },
                                success: function(data) {
                                  const resdata = JSON.parse(data);
                                  let options = '';
                                  resdata.forEach(item => {
                                    options += `<option value="${item.section_id}">${item.class_section}</option>`;
                                  });
                                  $("#classSectionId").html(options);

                                  $("#classSectionId").selectpicker({
                                    actionsBox: true,
                                    selectAllText: 'Select All',
                                    deselectAllText: 'Deselect All',
                                    selectedTextFormat: 'count > 3',
                                    countSelectedText: '{0} sections selected'
                                  }).selectpicker('refresh');

                                  if (filters.classSectionId && filters.classSectionId.length > 0) {
                                    $('#classSectionId').val(filters.classSectionId).selectpicker('refresh');
                                  }

                                  $('#classSectionId').on('changed.bs.select', function(e, clickedIndex, isSelected, previousValue) {
                                  // If the first option (Select Class/Section) is clicked
                                  if (clickedIndex === 0) {
                                    if (isSelected) {
                                      // If selecting the first option, deselect all others
                                      $(this).selectpicker('deselectAll');
                                      $(this).selectpicker('val', '');
                                    }
                                  } else if ($(this).val() && $(this).val().includes('')) {
                                    // If any other option is selected and the placeholder is still selected, remove the placeholder
                                    let currentValues = $(this).val();
                                    currentValues = currentValues.filter(val => val !== '');
                                    $(this).selectpicker('val', currentValues);
                                  }
                                  $(this).selectpicker('refresh');
                                });

                                  // $("#classSectionId").html(options).selectpicker('refresh');
                                  // $('#classSectionId').val(filters.classSectionId).selectpicker('refresh');
                                  // resolve();
                                }
                              });
                            });

                            let installmentFlow = Promise.resolve();
                            if (filters.installment_search === "true") {
                              $('#installmentType').show();
                              $('#installment').show();

                              installmentFlow = new Promise((resolve) => {
                                setTimeout(() => {
                                  updateSelect('#blueprint_type', filters.blueprint_type);
                                  resolve();
                                }, 200);
                              }).then(() => {
                                return new Promise((resolve) => {
                                  setTimeout(() => {
                                    $.ajax({
                                      url: '<?php echo site_url('reports/student/student_report/get_bpTypewise_insType') ?>',
                                      type: 'post',
                                      data: { bpType: filters.blueprint_type },
                                      success: function(data) {
                                        const parsed = JSON.parse(data);
                                        let output = '<option value="">Select Installments Type</option>';
                                        parsed.forEach(item => {
                                          output += `<option value="${item.id}">${item.name}</option>`;
                                        });
                                        $('#installment_type').html(output).val(filters.installment_type).trigger('change');
                                        resolve();
                                      }
                                    });
                                  }, 200);
                                });
                              }).then(() => {
                                return new Promise((resolve) => {
                                  setTimeout(() => {
                                    $.ajax({
                                      url: '<?php echo site_url('feesv2/reports/installment_type_installment') ?>',
                                      type: 'post',
                                      data: { installment_type: filters.installment_type },
                                      success: function(data) {
                                        const parsed = JSON.parse(data);
                                        let output = '';
                                        parsed.forEach(item => {
                                          output += `<option value="${item.id}">${item.name}</option>`;
                                        });
                                        $('#installmentId').html(output);
                                        $('#installmentId').selectpicker();
                                        $('#installmentId').val(filters.installmentId).selectpicker('refresh');
                                        resolve();
                                      }
                                    });
                                  }, 200);
                                });
                              });
                            }

                            Promise.all([classSectionFlow, installmentFlow]).then(() => {
                              $('#search').prop('disabled', false).val('Search').trigger('click');

                              setTimeout(() => {
                                applySavedColumnVisibility(JSON.stringify(visibleCols));
                              }, 1000); 
                            });

                          } else {
                            alert('Failed to fetch filter details. Please try again.');
                          }
                        }
                      });
                    }

                    function applySavedColumnVisibility(column_visibility) {
                      const tableSelectors = ['#fee_summary_data_summary', '#fee_summary_data'];
                      let visibleCols = [];

                      try {
                        visibleCols = JSON.parse(column_visibility || '[]').map(Number);
                      } catch (err) {
                        console.error('Failed to parse column_visibility JSON:', err);
                        return;
                      }

                      const waitForDataTable = (tableSelector) => {
                        const maxTries = 10;
                        let attempts = 0;

                        const interval = setInterval(() => {
                          const $table = $(tableSelector);
                          if ($.fn.DataTable.isDataTable($table)) {
                            const table = $table.DataTable();
                            table.columns().every(function(index) {
                              const shouldBeVisible = visibleCols.includes(index);
                              this.visible(shouldBeVisible, false);
                            });
                            table.columns.adjust().draw(false);
                            clearInterval(interval);
                          } else if (++attempts >= maxTries) {
                            clearInterval(interval);
                            console.warn(`⚠️ DataTable not initialized for ${tableSelector} after max attempts`);
                          }
                        }, 300);
                      };

                      tableSelectors.forEach(waitForDataTable);
                    }




                    function construct_summary_table(data) {
                      // console.log("Constructing summary table with data:", data);

                      // Ensure 'data' is an array before processing
                      if (!Array.isArray(data)) {
                        console.error("Invalid data format for summary table", data);
                        return;
                      }

                      let tableHtml = '<table class="table"><thead><tr><th>Column 1</th><th>Column 2</th></tr></thead><tbody>';

                      data.forEach(row => {
                        tableHtml += `<tr><td>${row.column1}</td><td>${row.column2}</td></tr>`;
                      });

                      tableHtml += '</tbody></table>';

                      $('#summary_table_container').html(tableHtml); // Make sure this div exists in your HTML
                    }
                  </script>
                <?php } ?>
              </div>
            </div>
          <?php } ?>
        </div>

        <div class="col-12 text-center loading-icon" style="display: none;">
          <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
        </div>
      </div>

      <div id="detailed">
        
        <div class="col-md-12 pt-2" style="overflow: hidden;">

          <div class="text-center"><div style="display: none;" class="progress" id="progress"><div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%"></div></div></div>
          <div class="total_summary">
          </div>
          <button type="button" data-toggle="modal" data-target="#summary" disabled="" style="display: none;" id="send_fee_sms"  class="btn btn-primary">Send SMS <?php echo ($email_option) ? '/ Email ' : '' ?></button>
          <div id="fees_student_status" class="fee_balance pt-3 table-responsive">
            
          </div>

          <label id="totalBalanSummary" style="display: none;">Total Balance Amount : <span class="totalBalanceAmount"></span></label><br>
           <div id="fees_balance_summary" class="fees_balance_summary pt-3 table-responsive" style="display: none;">
          </div>
        </div>
      </div>
    </div>
  </div> 
</div>



<script type="text/javascript">
  function print_fees_status(){
    var restorepage = document.body.innerHTML;
    $('.print_hide').css('display', 'none');
    var report_type = $('input[name="report_type"]:checked').val();
    if (report_type == 1) {
      var printcontent = document.getElementById('fees_student_status').innerHTML;
    }else{
      var printcontent = document.getElementById('fees_balance_summary').innerHTML;
    }

    document.body.innerHTML = printcontent;
    window.print();
    document.body.innerHTML = restorepage;
  }



  function exportToExcel_fee_status() {
  var htmls = "";
  var uri = "data:application/vnd.ms-excel;base64,";
  var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="(link unavailable)">' +
    '<head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->' +
    '<meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
  var base64 = function (s) {
    return window.btoa(unescape(encodeURIComponent(s)));
  };
  var format = function (s, c) {
    return s.replace(/{(\w+)}/g, function (m, p) {
      return c[p];
    });
  };
  var report_type = $("input[name='report_type']:checked").val();
  var mainTable = report_type == 1 ? $(".fee_balance").clone() : $(".fees_balance_summary").clone();
  var summaryTable = $(".total_summary").clone();
  var hiddenElements = $(".print_hide");
  hiddenElements.removeClass("print_hide");

  mainTable.find(".print_hide").remove();
  mainTable.find("#fee_summary_data_filter").remove();
  mainTable.find("#fee_summary_data_summary_filter").remove();
  mainTable.find(".dt-buttons").remove();
  var titleRow = '';
  htmls = titleRow + summaryTable.prop("outerHTML") + "<br><br>" + mainTable.prop("outerHTML");
  var ctx = { worksheet: "Spreadsheet", table: htmls };
  if (navigator.msSaveOrOpenBlob) {
    var blob = new Blob([format(template, ctx)], { type: "application/vnd.ms-excel" });
    navigator.msSaveOrOpenBlob(blob, "export.xls");
  } else {
    var link = document.createElement("a");
    link.download = "fees balance report.xls";
    link.href = uri + base64(format(template, ctx));
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
  hiddenElements.addClass("print_hide");
}
</script>
<style type="text/css">
  input[type='range'] {
    margin: 0 auto;
    width: 100px;
  }

  #sliderDiv {
    text-align: center;
    width: 350px;
    float: right;
  }
  .table>thead>tr>th{
    white-space: nowrap;
  }

 
	.autocomplete-items {
		position: absolute;
		overflow-y:auto;
		border-bottom: none;
		border-top: none;
		height:300px;
		margin:0px 15px;
		z-index: 99;
		/*position the autocomplete items to be the same width as the container:*/
		top: 100%;
		left: 0;
		right: 0;
	}
	.autocomplete-items div {
		padding: 10px;
		cursor: pointer;
		background-color: #fff; 
		border-bottom: 1px solid #d4d4d4; 
	}
	.autocomplete-items div:hover {
		/*when hovering an item:*/
		background-color: #e9e9e9; 
	}
	.autocomplete-active {
		/*when navigating through the items using the arrow keys:*/
		background-color: DodgerBlue !important; 
		color: #ffffff; 
	}

	.data-container {
		width: 100%;
	}
  
.buttons-print{
    padding: 2px  !important;
}
.buttons-colvis{
    padding: 2px !important;
}

.buttons-excel{
  border: none !important;
  background: none  !important;
  padding: 0  !important;
  margin: 0  !important;
}
.dt-button{
  border: none !important;
  background: none  !important;
}
.btn-info{
    border-radius: 8px !important; 
}
.dt-button .btn{
  line-height:20px;
}
.dt-buttons{
  text-align: right;
  float:right;
  /*position: absolute;*/
}
</style>
<script type="text/javascript">
  $('#installment_search').on('change',function(){
    if ($('#installment_search').is(':checked')) {
      $('#blueprintSelect').show();
      $('#multiBlueprintSelect').hide();
      $('.multiselect').removeAttr('id')
    }else{
      $('#blueprintSelect').hide();
      $('#multiBlueprintSelect').show();
      $('#installmentType').hide();
      $('#installment').hide();
      $('#installment_type').val('');
      $('#installmentId').val('');
      $('.multiselect').attr('id','blueprint_type');
    }
  });
</script>

<script>
$('.changeFeeType').on('change',function(){
  var bpType =  $('.changeFeeType').val();
  $('#installment_type').val('');
  $('#installmentType').hide();
  if (bpType != 'all') {
    changeInstallment_type(bpType);
    $('#installmentType').show();
  }else{
    $('#installmentId').val('');
    $('#installmentType').hide();
  }
});

  function changeInstallment_type(bpType){
    $.ajax({
      url:'<?php echo site_url('reports/student/student_report/get_bpTypewise_insType') ?>',
      type:'post',
      data : {'bpType':bpType},
      success : function(data){
        var data = JSON.parse(data);
        var output = '<option value="">Select Installments Type</option>';
        for(var i=0; i < data.length; i++){
          output += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
        }
        $('#installment_type').html(output);
      }
    });
  }
  function changeInstallment() {
    var installment_type = $('#installment_type').val();

    if (installment_type) {
      $.ajax({
        url: '<?php echo site_url('feesv2/reports/installment_type_installment') ?>',
        type: 'post',
        data: {
          'installment_type': installment_type
        },
        success: function(data) {
          var data = JSON.parse(data);
          var output;

          for (var i = 0; i < data.length; i++) {
            output += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
          }

          $('#installmentId').html(output);

          $('#installment').show();

          $('#installmentId').selectpicker('refresh');
        }
      });
    } else {
      $('#installment').hide();
      $('#installmentId').html('<option value="">Select Installments</option>');
    }
  }
  $('#installment_type').on('change',function(){
    changeInstallment();
  }); 


  $('input[name="report_type"]').change(function() {
    get_balance_report();
  });

  $(document).ready(function(){
    var fee_type = $('#blueprint_type').val();
    if (fee_type != null) {
      get_balance_report();
    }
  });
  var concession_total = 0;
  var balance_total = 0;
  var total_students = 0;
  var completed = 0;
  var summaryData = [];
  var is_semester_scheme = '<?php echo $this->settings->getSetting('is_semester_scheme') ?>';
  var combination_column = '<?php echo $this->settings->getSetting('puc_combination') ?>';

  function get_balance_report() {
    $('#search').prop('disabled', true).val('Please wait...');

    const report_type = $('input[name="report_type"]:checked').val();
    if (!report_type) {
      console.error("report_type is not defined");
      return;
    }

    // === Init UI for Fee Balance
    $('.loading-icon').show();
    concession_total = 0;
    balance_total = 0;
    total_students = 0;
    completed = 0;
    summaryData = [];

    $('.fee_balance').html('');
    $('.total_summary').html('');
    $('.total_student_summary').hide();

    if (report_type == 2) {
      $('#fees_balance_summary').show();
    } else {
      $('#fees_balance_summary, #totalBalanSummary, #totalFeeSummary').hide();
    }

    // === Init UI for Day Book
    $('#dataTableExport, #dataTablePrint').html('');
    $('#slider').val('0');
    $(".day_book, .day_book_summary").html('');
    $('#exportIcon, #exportIcon_summary, #print_visible, #print_summary').hide();
    $("#sliderDiv").hide();
    $('.prarthana_daily_reports').show();

    // === Get Inputs
    let fee_type = $('#blueprint_type').val() || 'all';
    let installment_type = $('#installment_type').val();
    let installmentId = $('#installmentId').val();
    let staff_kid = $('#staff_kid').val() || '';
    let rte_nrteId = $('#rte_nrteId').val() || '';
    let payment_status = $('#payment_status').val() || '';
    let donorsId = $('#donorsId').val() || '';
    let combination = $('#combination').val() || '';
    let admissionStatus = $('#admission_status').val() || '';
    const classId = $('#classId').val();
    const classSectionId = $('#classSectionId').val();
    const stakeholder_id = $('#stakeholder_id').val();
    const show_over_due = $('input[name="show_over_due"]:checked').val() || '';

    // === Step 1: Fee Balance AJAX
    $.ajax({
      url: '<?php echo site_url('feesv2/reports_v2/get_fee_balance_student_count'); ?>',
      type: "post",
      data: {
        installment_type,
        installmentId,
        fee_type,
        classSectionId,
        staff_kid,
        classId,
        rte_nrteId,
        payment_status,
        donorsId,
        combination,
        show_over_due,
        stakeholder_id,
        admissionStatus
      },
      success: function(data) {
        $('.loading-icon').hide();
        const students = JSON.parse(data);

        if (students.length > 0) {
          studentIds = students;
          total_students = parseInt(100 * (studentIds.length - 2)) + parseInt(studentIds[studentIds.length - 1].length);
          document.getElementById('progress-ind').style.width = (completed / total_students) * 100 + '%';
          $("#progress").show();
          callReportGetter(0);
        } else {
          $('.fee_balance').html('<h3>Result not found</h3>');
        }
      },
      error: function(err) {
        $('.loading-icon').hide();
        // console.log("Fee balance fetch error:", err);
      }
    });

    // === Step 2: Day Book / SMS Report AJAX
    const filters = {
      installment_search: $('#installment_search').is(':checked'),
      show_over_due: $('#show_over_due').is(':checked'),
      admission_status: admissionStatus,
      student_name_fees: $('#student_name_fees').val(),
      blueprint_type: fee_type,
      installment_type,
      installmentId,
      combination,
      rte_nrteId,
      staff_kid,
      payment_status,
      classId,
      classSectionId,
      donorsId,
      report_type,
    };


    // After both AJAX requests finish, enable the search button again
    $(document).ajaxStop(function() {
      $('#search').prop('disabled', false).val('Get Report');
    });
  }

  function check_all(check){
    if($(check).is(':checked')) {
      $(".student_id").prop('checked', true);
      $("#send_fee_sms").prop('disabled', false);
    }
    else {
      $(".student_id").prop('checked', false);
      $("#send_fee_sms").prop('disabled', true);
    }
  }

  function check_clicked(){
    if($('[name="student_id[]"]:checked').length > 0) {
      $("#send_fee_sms").prop('disabled', false);
    } else {
      $("#send_fee_sms").prop('disabled', true);
    }
  }

  function getReport(index) {
    var student_ids = studentIds[index];
    var classSectionId =  $('#classSectionId').val();
    var classId =  $('#classId').val();
    var installment_type = $('#installment_type').val();
    var installmentId = $('#installmentId').val();
    var fee_type = $('#blueprint_type').val();
    var staff_kid = $('#staff_kid').val();
    var payment_status = $('#payment_status').val();
    var donorsId = $('#donorsId').val();
    var report_type = $('input[name="report_type"]:checked').val();
    var show_over_due = $('input[name="show_over_due"]:checked').val();
    if (donorsId == undefined) {
      donorsId = '';
    }
    if (payment_status == undefined) {
      payment_status = '';
    }
    if (show_over_due == undefined) {
        show_over_due = '';
    }
    if (staff_kid == undefined) {
      staff_kid = '';
    }
    if (fee_type == null) {
      fee_type = 'all';
    }
    var url = '<?php echo site_url('feesv2/reports_v2/get_fee_balance_details'); ?>';
    if (report_type == 2) {
      url = '<?php echo site_url('feesv2/reports_v2/get_fee_balance_details_summary'); ?>';
    }
    $.ajax({
      url: url,
      data: {student_ids:student_ids, 'installment_type':installment_type,'installmentId':installmentId,'fee_type':fee_type,'classSectionId':classSectionId,'staff_kid':staff_kid,'classId':classId,'payment_status':payment_status, 'donorsId':donorsId,'show_over_due':show_over_due},
      type: "post",
      success: function (data) {
        var data = JSON.parse(data);
        var students = data.students;
        var bpSummaryBalance = data.bpSummaryBalance;
        var bpHeaderName = data.bpHeaderName;

        completed += Object.keys(students).length;
        var progress = document.getElementById('progress-ind');
        progress.style.width = (completed/total_students)*100+'%';
        construct_balance_summary(bpSummaryBalance, bpHeaderName);

        if (report_type == 1) {
          var headers = data.headers;
          var header = data.header;
       
          if (index == 0) {
            constructFeeHeader(header);
          }
          construct_balance_table(index, students, headers, header);
        }else{
          var header_summary = data.header_summary;
          if (index == 0) {
            constructFeeHeader_summary(header_summary);
          }
          construct_balance_table_summary(index, students, bpHeaderName);
        }
        // console.log(bpSummaryBalance);
        // console.log(students);
       
      },
      error: function (err) {
        console.log(err);
      }
    });
  }
  // function constructReport(index, students, headers, header) {
  //   $('#fee_summary_data').append(construct_balance_table(index, students, headers, header));
  //   index++;
  //   callReportGetter(index);
  // }
  
  
  function constructFeeHeader(header) {
    var h_output = '<table id="fee_summary_data" class="table table-bordered">';
    h_output += header;
    h_output += '<tbody id="fee_append_data">';
    h_output += '</tbody>';
    h_output += '</table>';
    $('.fee_balance').html(h_output);
  }

 


  function constructFeeHeader_summary(header) {
    var h_output = '<table id="fee_summary_data_summary" class="table table-bordered">';
    h_output += header;
    h_output += '</table>';
    $('.fees_balance_summary').html(h_output);
  }

  function construct_balance_table_summary(index1, students, bpHeaderName) {
    var srNo1 = index1 * 100;
    html = '';
    var j=0;
    for(var i in students) {
      if (students[i].admission_status !='2') {
        statusColor ='#d30c0c';
      }else{
        statusColor ='';
      }
      html += '<tr style="color:'+statusColor+'">';
      html += '<td>'+(j+1+srNo1)+'</td>';
      html += '<td>'+students[i].student_name+'</td>';
      html += '<td>'+students[i].class_name+'</td>';
      html += '<td>'+students[i].admission_no+'</td>';
      html += '<td>'+students[i].enrollment_number+'</td>';
      html += '<td>'+students[i].category+'</td>';
      html += '<td>'+students[i].caste+'</td>';
      html += '<td>'+students[i].student_mobile_no+'</td>';
      if (is_semester_scheme == 1) {
        if (students[i].semester == null) {
          html +='<td></td>';
        }else{
          html +='<td>'+students[i].semester+'</td>';
        }
      }
      if(combination_column == 1){
        html += '<td>'+students[i].combination+'</td>';
      }
      html += '<td>'+students[i].father_name+'</td>';
      html += '<td>'+students[i].f_number+'</td>';
      html += '<td>'+students[i].mother_name+'</td>';
      html += '<td>'+students[i].m_number+'</td>';
      html += '<td>'+numberToCurrency(students[i].total_amount)+'</td>';
      html += '<td>'+numberToCurrency(students[i].total_balance)+'</td>';
      html += '<td>'+numberToCurrency(students[i].total_fine)+'</td>';
      html += '<td>'+numberToCurrency(students[i].total_balance + students[i].total_fine)+'</td>';
      var std_bp = students[i].bpId_summary;
      var std_bp_fee_amount = students[i].bpId_summary_amount;
      for(var bpId in bpHeaderName){
        if (std_bp[bpId]) {
          html += '<td>'+numberToCurrency(std_bp_fee_amount[bpId])+'</td>';
          html += '<td>'+numberToCurrency(std_bp[bpId])+'</td>';
        }else{
          html += '<td>-</td>';
          html += '<td>-</td>';
        }
      }
      html += '</tr>';
      j++;
    }
    $('#fee_summary_data_summary').append(html);
    index1++;
    callReportGetter(index1);
  }
  var columnNames = [];
  function construct_balance_table(index, students, headers, header) {

    var srNo = index * 100;

    html = '';
    var j=0;
    for(var i in students) {
      if (students[i].admission_status !='2') {
        statusColor ='#d30c0c';
      }else{
        statusColor ='';
      }

      html += '<tr style="color:'+statusColor+'">';
      html += '<td class="print_hide"><input type="checkbox" onclick="check_clicked()" name="student_id[]" class="student_id print_hide" value='+students[i].stdId+'></td>';
      html += '<td>'+(j+1+srNo)+'</td>';
      html += '<td>'+students[i].student_name+'</td>';
      html += '<td>'+students[i].class_name+'</td>';
      html += '<td>'+students[i].admission_no+'</td>';
      html += '<td>'+students[i].enrollment_number+'</td>';
      html += '<td>'+students[i].category+'</td>';
      html += '<td>'+students[i].caste+'</td>';
      html += '<td>'+students[i].student_mobile_no+'</td>';
      if (is_semester_scheme == 1) {
        if (students[i].semester == null) {
          html +='<td></td>';
        }else{
          html +='<td>'+students[i].semester+'</td>';
        }
      }
      if(combination_column == 1){
        html += '<td>'+students[i].combination+'</td>';
      }
      html += '<td>'+students[i].father_name+'</td>';
      html += '<td>'+students[i].f_number+'</td>';
      html += '<td>'+students[i].mother_name+'</td>';
      html += '<td>'+students[i].m_number+'</td>';
      html += '<td>'+numberToCurrency(students[i].total_amount)+'</td>';
      html += '<td>'+numberToCurrency(students[i].total_balance)+'</td>';
      html += '<td>'+numberToCurrency(students[i].total_fine)+'</td>';
      html += '<td>'+numberToCurrency(students[i].total_balance + students[i].total_fine)+'</td>';
      var std_bp = students[i].bpId;
      for(var hbpId in headers) {
        var insHeader = headers[hbpId];
        for(var hinsId in insHeader) {
          columnNames.push(insHeader[hinsId] + ' - ' + hinsId);
          if(hbpId in std_bp && hinsId in std_bp[hbpId]) {
            html += '<td>'+numberToCurrency(std_bp[hbpId][hinsId])+'</td>';
          } else {
            html += '<td>-</td>';
          }
        }
      }
      html += '</tr>';
      j++;
    }
    $('#fee_append_data').append(html);
    index++;
    callReportGetter(index);
  }


  function construct_balance_summary(summary, headerbpName) {
    for(bp in headerbpName){
      if ((summary).hasOwnProperty(bp)) {
        if (!(summaryData).hasOwnProperty(headerbpName[bp].blueprint_name)) {
          summaryData[headerbpName[bp].blueprint_name] = [];
          summaryData[headerbpName[bp].blueprint_name]['total_fee_balance'] = 0;
          summaryData[headerbpName[bp].blueprint_name]['total_fee_amount'] = 0;
          summaryData[headerbpName[bp].blueprint_name]['total_balance_student'] = 0;
        }
        summaryData[headerbpName[bp].blueprint_name]['total_fee_balance'] += summary[bp].total_balance_summary;
        summaryData[headerbpName[bp].blueprint_name]['total_fee_amount'] += summary[bp].total_amount_summary;
        summaryData[headerbpName[bp].blueprint_name]['total_balance_student'] += summary[bp].total_balance_student;
      }
    }
  }


  function callReportGetter(index){
    if(index < studentIds.length) {
      getReport(index);
    } else {
      $("#progress").hide();
      $('#exportIcon').show();
      var report_type = $('input[name="report_type"]:checked').val();
      if (report_type == 1) {
        $('#fees_balance_summary').hide();
        $('#totalBalanSummary').hide();
        $('#totalFeeSummary').hide();
        $('#send_fee_sms').show();
        $('.total_student_summary').show();
        fee_summary = '<div class="table-responsive">';
        fee_summary +='<table class="table table-bordered">';
        fee_summary +='<thead>';
        fee_summary +='<tr>';
        fee_summary +='<th style="font-size:14px; color:#7ea3d2">Fee Type</th>';
        for(var bp_name in summaryData) {
          fee_summary +='<th><b>'+bp_name+'</b></th>';
        }
       
        fee_summary +='</tr>';
        fee_summary +='</thead>';
        fee_summary +='<tbody>';
        fee_summary += '<tr>';
        fee_summary +='<th style="font-size:14px; color:#7ea3d2">Balance</th>';
        for(var bp_name in summaryData) {
          fee_summary +='<td>'+numberToCurrency(summaryData[bp_name]['total_fee_balance'])+'</td>';
        }
        fee_summary += '</tr>';
        fee_summary += '<tr>';
        fee_summary +='<th style="font-size:14px; color:#7ea3d2"># of Student</th>';
        for(var bp_name in summaryData) {
          fee_summary +='<td>'+summaryData[bp_name]['total_balance_student']+'</td>';
        }
        fee_summary += '</tr>';
        fee_summary +='</tbody>';
        fee_summary +='</table>';
        fee_summary += '</div>';
        $(".total_summary").html(fee_summary);

        let colLen = 16;
        if (combination_column == 1 && is_semester_scheme == 1) {
          colLen = 18;
        } else if (combination_column == 1 || is_semester_scheme == 1) {
          colLen = 17;
        }
      let table = $('#fee_summary_data').DataTable({
        ordering: false,
        paging: false,
        dom: 'Bfrtip',
        info: false,
        'columnDefs':  [
            { orderable: false, targets: 1 },
            {targets : 6, visible: false},
            {targets : 7, visible: false},
            {targets : 8, visible: false}
          ],
        buttons: [
          {
            extend: 'print',
            text: '<button class="btn btn-info"><span class="fa fa-print" aria-hidden="true"></span> Print</button>',
            title: 'Fee Balance report',
            footer: true,
            exportOptions: {
              columns: function (idx, data, node) {
                return table.column(idx).visible() && idx !== 0;
              }
            },
            customize: function (win) {
              $(win.document.body)
                .prepend($('.total_summary').clone())
                .css('font-size', '10pt');
                $(win.document.head).append(`
                <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
                <style>
                  @page {
                    size: auto;
                    margin: 12mm;
                  }

                  body {
                    font-family: 'Poppins', sans-serif;
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                    color: #333;
                    background: #fff;
                  }

                  h2, h3 {
                    text-align: center;
                    margin-bottom: 15px;
                    font-weight: 500;
                  }

                  table {
                    border-collapse: collapse !important;
                    width: 100% !important;
                    margin-top: 20px;
                    font-size: 10pt;
                    color: #333;
                  }

                  th, td {
                    border: 1px solid #ccc !important;
                    padding: 8px 12px;
                    text-align: left;
                    vertical-align: middle;
                  }

                  th {
                    background-color: #f4f7fc !important;
                    font-weight: 600;
                    color: #333;
                  }

                  .table-bordered {
                    width: 100% !important;
                  }

                  .fee_summary table {
                    margin-bottom: 25px;
                  }

                  .fee_summary th[colspan] {
                    text-align: center;
                    background: #e0ecff;
                    font-size: 11pt;
                    font-weight: 500;
                  }

                  tfoot th {
                    background-color: #f9f9f9;
                    font-weight: 600;
                  }

                  .distrubanceAmount {
                    color: #007bff;
                    text-decoration: underline;
                    font-size: 10pt;
                  }

                  a {
                    color: #007bff !important;
                  }
                </style>
              `);
            },
          },
          {
            text: '<button class="btn btn-info"><span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel</button>',
            title: 'Fee Balance report',
            exportOptions: {
              columns: ':visible:not(:first-child)', 
            },
            action: function () {
              exportToExcel_fee_status();
            },
          },
        ],
      });
      initializeColVisButton(table, 'fee_summary_data_wrapper', colLen);

      }else{
        var totalBalanceAmount = 0;
        var totalFeeAmount = 0;
        for(var bp_name in summaryData) {
          totalBalanceAmount += parseFloat(summaryData[bp_name]['total_fee_balance'],2);
          totalFeeAmount += parseFloat(summaryData[bp_name]['total_fee_amount']);
        }
        totalFeeAmount = totalFeeAmount.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
        totalBalanceAmount = totalBalanceAmount.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });

        $('.totalBalanceAmount').html(totalBalanceAmount);
        $('.totalFeeAmount').html(totalFeeAmount);
        $('#send_fee_sms').hide();
        $('#fees_balance_summary').show();
        $('#totalBalanSummary').show();
        $('#totalFeeSummary').show();

        let colLen = 15;
        if (combination_column == 1 && is_semester_scheme == 1) {
          colLen = 17;
        } else if (combination_column == 1 || is_semester_scheme == 1) {
          colLen = 16;
        }

        let table1 = $('#fee_summary_data_summary').DataTable({
          ordering: false,
          paging: false,
          dom: 'Bfrtip',
          info: false,
          'columnDefs':  [
            { orderable: false, targets: 1 },
            {targets : 5, visible: false},
            {targets : 6, visible: false},
            {targets : 7, visible: false}
          ],
          buttons: [
            {
              extend: 'print',
              text: '<button class="btn btn-info"><span class="fa fa-print" aria-hidden="true"></span> Print</button>',
              title: 'Fee Balance report',
              footer: true,
              exportOptions: {
              columns: ':visible', 
            },
              customize: function (win) {
                $(win.document.body)
                  .prepend($('.total_summary').clone())
                  .css('font-size', '10pt');
                  $(win.document.head).append(`
                  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
                  <style>
                    @page {
                      size: auto;
                      margin: 12mm;
                    }

                    body {
                      font-family: 'Poppins', sans-serif;
                      -webkit-print-color-adjust: exact;
                      print-color-adjust: exact;
                      color: #333;
                      background: #fff;
                    }

                    h2, h3 {
                      text-align: center;
                      margin-bottom: 15px;
                      font-weight: 500;
                    }

                    table {
                      border-collapse: collapse !important;
                      width: 100% !important;
                      margin-top: 20px;
                      font-size: 10pt;
                      color: #333;
                    }

                    th, td {
                      border: 1px solid #ccc !important;
                      padding: 8px 12px;
                      text-align: left;
                      vertical-align: middle;
                    }

                    th {
                      background-color: #f4f7fc !important;
                      font-weight: 600;
                      color: #333;
                    }

                    .table-bordered {
                      width: 100% !important;
                    }

                    .fee_summary table {
                      margin-bottom: 25px;
                    }

                    .fee_summary th[colspan] {
                      text-align: center;
                      background: #e0ecff;
                      font-size: 11pt;
                      font-weight: 500;
                    }

                    tfoot th {
                      background-color: #f9f9f9;
                      font-weight: 600;
                    }

                    .distrubanceAmount {
                      color: #007bff;
                      text-decoration: underline;
                      font-size: 10pt;
                    }

                    a {
                      color: #007bff !important;
                    }
                  </style>
                `);
              },
            },
            {
              text: '<button class="btn btn-info"><span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel</button>',
              title: 'Fee Balance report',
              action: function () {
                exportToExcel_fee_status();
              },
            },
          ],
        });
        initializeColVisButton(table1, 'fee_summary_data_summary_wrapper', colLen);
      }
      $('.fees_student_status').on('click', '.buttons-print, .buttons-excel', function () {
          $('.fees_student_status').prepend($('.total_summary').clone());
      });
    }
  }

  function initializeColVisButton(table, wrapperId, colLen) {
  // Add a column visibility button to the given table
  new $.fn.dataTable.Buttons(table, {
    buttons: [
      {
        extend: 'colvis',
        text: '<button class="btn btn-info"><span class="fa fa-columns" aria-hidden="true"></span> Columns</button>',
        className: 'btn btn-info',
        columns: function (idx, data, node) {
          return idx <= colLen; // Restrict to columns <= 13
        },
      },
    ],
  }).container().appendTo($(`#${wrapperId} label`));
}
</script>

<style>
.dt-button-collection .buttons-columnVisibility:before {
  content: ' ';
  margin-top: -6px;
  margin-left: 10px;
  border: 1px solid black;
  border-radius: 3px;
}

.dt-button-collection .buttons-columnVisibility:before,
.dt-button-collection .buttons-columnVisibility.active span:before {
  display: block;
  position: absolute;
  top: 1.2em;
  left: 0;
  width: 12px;
  height: 12px;
  box-sizing: border-box;
}

.dt-button-collection .buttons-columnVisibility span {
  margin-left: 20px;
}

.dt-button-collection .buttons-columnVisibility.active span:before {
  content: '\2714'; /* Unicode checkmark character */
  margin-top: -13px;
  margin-left: 12px;
  text-align: center;
  text-shadow: 1px 1px #DDD, -1px -1px #DDD, 1px -1px #DDD, -1px 1px #DDD;
}

div.dt-button-collection .dt-button {
  position: relative;
  left: 0;
  right: 0;
  width: 100%;
  display: block;
  float: none;
  background: none;
  margin: 0;
  padding: 1px 1rem;
  border: none;
  text-align: left;
  cursor: pointer;
  color: inherit;
}

/* ✅ Fix: Ensure dropdown scrolls and avoids footer cutoff */
div.dt-button-collection {
  position: absolute;
  top: 100%;
  left: 0;
  width: 220px;
  max-height: 300px; /* <- forces scrollbar when needed */
  overflow-y: auto !important; /* <- ensure it's scrollable */
  overflow-x: hidden;
  margin-top: 3px;
  padding: 0.5em 0;
  border: 1px solid rgba(0, 0, 0, 0.4);
  background-color: #fff;
  z-index: 9999;
  border-radius: 6px;
  box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.2);
  box-sizing: border-box;
}

/* ✅ Ensure body and parent containers don't block scroll */
body,
html {
  height: 100%;
  overflow: visible !important;
}

.dataTables_wrapper {
  position: relative;
  overflow: visible !important;
}

/* Optional: Better-looking scrollbar */
div.dt-button-collection::-webkit-scrollbar {
  width: 6px;
}
div.dt-button-collection::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}
div.dt-button-collection::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.5);
}

  </style>
<script type="text/javascript">
  function numberToCurrency(amount) {
    var formatter = new Intl.NumberFormat('en-IN', {
      // style: 'currency',
      currency: 'INR',
    });
    return formatter.format(amount);
  }
   function checkCredits() {
    var c_type = $("#communication_type").val();
    var template = $("#templateId").val();

    $("#confirmBtn").prop('disabled', true).html('Please wait...');
    var sent_to = $("#sent_to").val();
    $(".loader-background").show();
    if(c_type == 'notification') {
      $("#balance_sms-form").submit();
    }else if(c_type == 'email'){
      if ($('#emailTemplate').val() == '') {
        alert('Please select email template');
        $("#confirmBtn").prop('disabled', false).html('Confirm');
        return false;
      };
      $("#balance_sms-form").submit();
    } else {
      if (template == '') {
        alert('SMS Format Not selected')
        $("#confirmBtn").prop('disabled', false).html('Confirm');
        return false;
      }
      var sms_count = 0;
      if(sent_to == 'Both') {
        if(c_type == 'sms') {
          sms_count = father_numbers + mother_numbers;
        } else {
          sms_count = (father_numbers + mother_numbers) - (father_no_tokens + mother_no_tokens);
        }
      } else if(sent_to == 'Father') {
        if(c_type == 'sms') {
          sms_count = father_numbers;
        } else {
          sms_count = (father_numbers) - (father_no_tokens);
        }
      } else if(sent_to == 'Mother') {
        if(c_type == 'sms') {
          sms_count = mother_numbers;
        } else {
          sms_count = (mother_numbers) - (mother_no_tokens);
        }
      } else if(sent_to == 'preferred_parent') {
        if(c_type == 'sms') {
          sms_count = preferred_f_parent + preferred_m_parent;
        } else {
          sms_count = (preferred_f_parent + preferred_m_parent) - (father_no_tokens + mother_no_tokens);
        }
      } else {
        sms_count = preferred_number;
      }

      $.ajax({
        url: '<?php echo site_url('feesv2/reports/check_sms_credits'); ?>',
        type: "post",
        data: {sms_count:sms_count, message: credit_message},
        success: function (data) {
          console.log(data);
          if(data == 1) {
            $("#balance_sms-form").submit();
          } else {
            $("#credits-err").show();
          }
          $(".loader-background").hide();
        },
        error: function (err) {
          console.log(err);
        }
      });
    }
  }

  function changeSMSformat() {
    $.ajax({
      url: '<?php echo site_url('feesv2/reports/get_sms_template_for_balance_fee'); ?>',
      type: "post",
      success: function (data) {
        // console.log(data);
        var data = JSON.parse(data);
        $('#sms-tempalte').html(construct_select_sms_template(data));
      },
      error: function (err) {
        console.log(err);
      }
    });
  }
  
  function construct_select_sms_template(data) {
    var smsTemp = '';
    smsTemp += '<div class="form-group">';
    smsTemp += '<label class="col-md-3 control-label">Select Template</label>';
    smsTemp += '<div class="col-md-5">';
    smsTemp += '<select class="form-control" onchange="change_template()" name="select_template" id="templateId">';
    smsTemp+='<option value="">Select</option>';
    // for (var i = 0; i < data.length; i++) {
    //   smsTemp+='<option value="'+data[i].id+'">'+data[i].name+'</option>';
    // }
     smsTemp+='<option value="custom">Custom</option>';
     smsTemp+='<option value="default">Format-1</option>';
     smsTemp+='<option value="default1">Format-2</option>';
     smsTemp+='<option value="default2">Format-3</option>';
     smsTemp+='<option value="default3">Format-4</option>';
    smsTemp +='</select>';
    smsTemp +='</div>';
    smsTemp += '</div';
    return smsTemp;
  }
  var mapingStds = [];
  var credit_message = '';
  var school_name = '<?php echo $this->settings->getSetting('school_name') ?>';
  function change_template() {
    var templateId = $('#templateId').val();
     if (templateId !='') {
      $('.sms-content').show();
      $('#selectBtn').prop('disabled',false);
    }else{
      $('.sms-content').hide();
      $('#selectBtn').prop('disabled',true);
    }
     content = '';
    $.ajax({
      url: '<?php echo site_url('feesv2/reports_v2/get_sms_template_content_for_balance_fee'); ?>',
      type: "post",      
      data:{'templateId':templateId},
      success: function (data) {
        var sms_content_data = JSON.parse(data);
        content = sms_content_data.content;
         for (var m in mapingStds) {
          var message = content;
          message = message.replace('%%student_name%%',$("#mapingStdName_"+mapingStds[m]).val(), '%%class_section%%',$("#mapingClassName_"+mapingStds[m]).val());
          message = message.replace('%%class_section%%',$("#mapingClassName_"+mapingStds[m]).val());
          message = message.replace('%%school_name%%',school_name);
          message = message.replace('%%balance%%',$("#mapingBalance_"+mapingStds[m]).val());
          message = message.replace('%%balance_fine%%',$("#mapingFineBalance_"+mapingStds[m]).val());
          message = message.replace('%%fine_amount%%',$("#mapingFine_"+mapingStds[m]).val());
          message = message.replace('%%installment_amount%%',$("#mapingIns_bal_amount_"+mapingStds[m]).val());
          message = message.replace('%%installment_wise_balance_with_due_date%%',$("#mapingMessage_with_due_date_"+mapingStds[m]).val());
          message = message.replace('%%installment_wise_balance_without_due_date%%',$("#mapingMessage_without_due_date_"+mapingStds[m]).val());
          $('#sms_message_body_'+mapingStds[m]).html(message);
          $(".std_ids_"+mapingStds[m]).val(message);
          if(credit_message.length < message.length) {
            credit_message = message;
          }
        }
      },
      error: function (err) {
        console.log(err);
      }
    });     
  }

  function change_email_template() {
    var emailtemplateId = $('#emailTemplate').val();
    if (emailtemplateId=='') {
      $('.email_content').html('Select Template');
    }
    $.ajax({
      url: '<?php echo site_url('feesv2/reports_v2/get_fee_balance_email_content'); ?>',
      type: 'POST',
      data: {'emailtemplateId':emailtemplateId},
    })
    .done(function(data){
      if (data==0) {
        $('.email_content').html('');
        return false;
      }
      var reData = $.parseJSON(data);
      var email_content = reData.content;
      for (var m in mapingStds) {
        var message = email_content.replace(/(<([^>]+)>)/ig,"");
        var messageEmail = email_content;
        message = message.replace('%%student_name%%',$("#mapingStdName_"+mapingStds[m]).val(), '%%class_section%%',$("#mapingClassName_"+mapingStds[m]).val());
        message = message.replace('%%class_section%%',$("#mapingClassName_"+mapingStds[m]).val());
        message = message.replace('%%balance%%',$("#mapingBalance_"+mapingStds[m]).val());
        message = message.replace('%%balance_fine%%',$("#mapingFineBalance_"+mapingStds[m]).val());
        message = message.replace('%%fine_amount%%',$("#mapingFine_"+mapingStds[m]).val());
        message = message.replace('%%installment_amount%%',$("#mapingIns_bal_amount_"+mapingStds[m]).val());
        message = message.replace('%%installment_wise_balance_with_due_date%%',$("#mapingMessage_with_due_date_"+mapingStds[m]).val());
        message = message.replace('%%installment_wise_balance_without_due_date%%',$("#mapingMessage_without_due_date_"+mapingStds[m]).val());

        messageEmail = messageEmail.replace('%%student_name%%',$("#mapingStdName_"+mapingStds[m]).val(), '%%class_section%%',$("#mapingClassName_"+mapingStds[m]).val());
        messageEmail = messageEmail.replace('%%class_section%%',$("#mapingClassName_"+mapingStds[m]).val());
        messageEmail = messageEmail.replace('%%balance%%',$("#mapingBalance_"+mapingStds[m]).val());

        messageEmail = messageEmail.replace('%%balance_fine%%',$("#mapingFineBalance_"+mapingStds[m]).val());
        messageEmail = messageEmail.replace('%%fine_amount%%',$("#mapingFine_"+mapingStds[m]).val());
        messageEmail = messageEmail.replace('%%installment_amount%%',$("#mapingIns_bal_amount_"+mapingStds[m]).val());
        messageEmail = messageEmail.replace('%%installment_wise_balance_with_due_date%%',$("#mapingMessage_with_due_date_"+mapingStds[m]).val());
        messageEmail = messageEmail.replace('%%installment_wise_balance_without_due_date%%',$("#mapingMessage_without_due_date_"+mapingStds[m]).val());

        $('#sms_message_body_'+mapingStds[m]).html(message);
        $(".std_ids_"+mapingStds[m]).val(messageEmail);

      }


    });

  }


  function save_sms_content() {
    var content = $('.sms-content').val();
    if (content=='') {
      return false;
    }
    $('#smsContent').val(content);
    $('#smsContenthtml').html(content);
    $('#sms_template').modal('hide');
  }



  $('#send_fee_sms').click(function(){
    $('#dynamic-content').html('');
    $('#modal-loader-content').show();
     $('#confirmBtn').val('Please wait..').attr('disabled','disabled');
      var stdIds = [];
      $('.student_id:checked').each(function(){
        stdIds .push($(this).val());
        mapingStds .push($(this).val());
      });
      getsmsPreviewReport(stdIds);
      // while(stdIds.length) {
      //   var student_ids = stdIds.splice(0,100);
      //   console.log(student_ids);
      // }
       
      
  });

  function getsmsPreviewReport(stdIds) {
     var cType =  $('#communication_type').val();
      
    if(stdIds.length > 0) {
      $('#confirmBtn').val('Please wait..').attr('disabled','disabled');
      $('#communication_type').prop('disabled',true);
      $('#communication_type').val('sms');
      change_cTypeAction();
      $('#templateId').val('');
      var student_ids = stdIds.splice(0,50);
      var classSectionId =  $('#classSectionId').val();
      var installment_type = $('#installment_type').val();
      var installmentId = $('#installmentId').val();
      var fee_type = $('#blueprint_type').val();
      var show_over_due = $('input[name="show_over_due"]:checked').val();
      if (show_over_due == undefined) {
        show_over_due = '';
      }
      if (fee_type == null) {
        fee_type = 'all';
      }

      $.ajax({
        url: '<?php echo site_url('feesv2/reports_v2/get_fee_balance_sms_details'); ?>',
        data: {'installment_type':installment_type,'installmentId':installmentId,'fee_type':fee_type,'student_ids':student_ids,'classSectionId':classSectionId,'show_over_due':show_over_due},
        type: "post",
        success: function (data) {
          console.log(data);
          
          var data = JSON.parse(data);
          construct_balance_smsm_table(data);
          getsmsPreviewReport(stdIds);
          
        },
        error: function (err) {
          console.log(err);
        }
      });
    }else{
      $('#confirmBtn').val('Confirm').removeAttr('disabled');
      $('#communication_type').prop('disabled',false);
    }
  }

var father_numbers = 0;
var father_no_tokens = 0;
var mother_numbers = 0;
var mother_no_tokens = 0;
var preferred_number = 0;
var preferred_f_parent = 0;
var preferred_m_parent = 0;
function construct_balance_smsm_table(smsprivew) {
  var send_to = $("#sent_to").val();
  var html = '';
  $('#modal-loader-content').show();
    // html += '<table class="table table-bordered">';
    // html += '<thead>';
    // html += '<tr>';
    // html += '<th width="25%">Student Name</th>';
    // html += '<th>Message</th>';
    // html += '</tr>';
    // html += '</thead>';
    // html += '<tbody>';
    var due_cross = '';
    if ($('#due_cross').is(":checked")) {
      due_cross = 1;
    }
    for (var key in smsprivew) {
      // var smsContent = $('#smsContent').val();
      var message = 'Select format';
      // message = message.replace('%%student_name%%',smsprivew[key].student_name);
      // message = message.replace('%%class_section%%',smsprivew[key].class_name);
      // message = message.replace('%%balance%%',smsprivew[key].balance);
      // // message = message.replace('%%installment_amount%%',smsprivew[key].ins_bal_amount);
      // message = message.replace('%%installment_wise_balance_with_due_date%%',smsprivew[key].message_with_due_date);
      // message = message.replace('%%installment_wise_balance_without_due_date%%',smsprivew[key].message_without_due_date);
      // if(credit_message.length < message.length) {
      //   credit_message = message;
      // }
    html += '<tr>';
    html += '<input type="hidden" id="mapingStdName_'+smsprivew[key].std_id+'" value="'+smsprivew[key].student_name+'">';
    html += '<input type="hidden" id="mapingClassName_'+smsprivew[key].std_id+'" value="'+smsprivew[key].class_name+'">';
    html += '<input type="hidden" id="mapingBalance_'+smsprivew[key].std_id+'" value="'+smsprivew[key].balance+'">';

    html += '<input type="hidden" id="mapingFineBalance_'+smsprivew[key].std_id+'" value="'+smsprivew[key].balancewithfine+'">';
    html += '<input type="hidden" id="mapingFine_'+smsprivew[key].std_id+'" value="'+smsprivew[key].fine_amount+'">';
    html += '<input type="hidden" id="mapingIns_bal_amount_'+smsprivew[key].std_id+'" value="'+smsprivew[key].ins_bal_amount+'">';
    html += '<input type="hidden" id="mapingMessage_with_due_date_'+smsprivew[key].std_id+'" value="'+smsprivew[key].message_with_due_date+'">';
    html += '<input type="hidden" id="mapingMessage_without_due_date_'+smsprivew[key].std_id+'" value="'+smsprivew[key].message_without_due_date+'">';
    html += '<td>'+smsprivew[key].student_name;
    var number = smsprivew[key].preferred_number;
    if(number == '' || number == null) {
      number = '<span class="text-danger">No Number</span>';
    }
    var fEmail = smsprivew[key].fatherEmail;
    if(fEmail == '' || fEmail == null) {
      fEmail = '<span class="text-danger">No Email</span>';
    }
    var mEmail = smsprivew[key].motherEmail;
    if(mEmail == '' || mEmail == null) {
      mEmail = '<span class="text-danger">No Email</span>';
    }
    var style = 'style="display:none;"';
    if(send_to == 'preferred')
      style = '';
    html += '<span '+style+' class="preferred"><br>('+number+')</span>';
    html += '</td>';
    html += '<td class="email_dispaly_only emailFather" style="display: none;">'+smsprivew[key].father_name+'<br>( '+fEmail+' )<input type="hidden" name="email['+smsprivew[key].std_id+'][]" value="'+fEmail+'"></td>';
    html += '<td class="email_dispaly_only eamilMother" style="display: none;">'+smsprivew[key].mother_name+'<br>( '+mEmail+' )<input type="hidden" name="email['+smsprivew[key].std_id+'][]" value="'+mEmail+'"></td>';
    html += '<td class="sms_dispaly_only" id="sms_message_body_'+smsprivew[key].std_id+'" >'+message+'</td>';
    html += '<input type="hidden" name="std_id['+smsprivew[key].std_id+']" class="std_ids_'+smsprivew[key].std_id+' clearmessage" value="'+message+'">';
    html += "</tr>";

    //for credits calculation
    if(smsprivew[key].preferred_number) {
      preferred_number++;
    }
    if(smsprivew[key].preferred_parent == 'Both') {
      if(smsprivew[key].f_number)
        preferred_f_parent++;
      if(smsprivew[key].m_number)
        preferred_m_parent++;
    } else if(smsprivew[key].preferred_parent == 'Father') {
      if(smsprivew[key].f_number)
        preferred_f_parent++;
    } else {
      if(smsprivew[key].m_number)
        preferred_m_parent++;
    }

    if(smsprivew[key].m_number) {
      mother_numbers++
    }

    if(smsprivew[key].f_number) {
      father_numbers++
    }

    if(smsprivew[key].m_token == '' || smsprivew[key].m_token == null) {
      mother_no_tokens++
    }
    if(smsprivew[key].f_token == '' || smsprivew[key].f_token == null) {
      father_no_tokens++
    }
  }
  // html += '</tbody>';
  // html += '</table>';
  $('#dynamic-content').append(html);
  $('#modal-loader-content').hide();
}

function showPreferred() {
  var val = $("#sent_to").val();
  var CType =  $('#communication_type').val();
  if (CType == 'email') {
     if (val == 'Father') {
      $('.emailFather').show();
      $('.eamilMother').hide();
    }else if(val == 'Mother'){
      $('.emailFather').hide();
      $('.eamilMother').show();
    }else if(val =='Both'){
      $('.emailFather').show();
      $('.eamilMother').show();
    }
  }

  if(val == 'preferred') {
    $(".preferred").show();
  } else {
    $(".preferred").hide();
  }
}
</script>
<div id="sms_template" class="modal fade" role="dialog">
  <div class="modal-dialog">
  <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Select SMS Template</h4>
      </div>
      <div id="numberBody" class="modal-body table-responsive" style="overflow-y:auto;">
        <div id="modal-loader" style="display: none; text-align: center;">
            <!-- ajax loader -->
          <img src="<?php echo base_url('assets/img/ajax-loader.gif');?>" style="width:400px; height:400px;">
       </div>
        <table class="table" id="sms-tempalte" width="100%">
            
        </table>
        <span class="help-block" id="html_format" style="color: red;margin-bottom: 10px;"></span>
        <textarea class="form-control sms-content" placeholder="Message" style="display: none;"></textarea>
        <span class="help-block">%%student_name%% , %%class_section%%, %%installment_wise_balance_with_due_date%%, %%installment_wise_balance_without_due_date%%, %%balance%%</span>
      </div>
      <div class="modal-footer">
        <input type="button" id="selectBtn" onclick="save_sms_content()"  disabled="true" class="btn btn-warning" value="Select">
        <button type="button" id="cancelModal" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
      </div>
    </div>
  </div>
</div>

<form autocomplete="off" enctype="multipart/form-data" method="post" id="balance_sms-form" action="<?php echo site_url('feesv2/reports_v2/send_balance_sms') ?>" data-parsley-validate="" class="form-horizontal"> 
<div id="summary" class="modal fade" role="dialog">
    <div class="modal-dialog">
    <!-- Modal content-->
      <div class="modal-content" style="width: 80%;margin: auto;">
       
          <div class="modal-header">
            <h4 class="modal-title">Send SMS <?php echo ($email_option) ? '/ Email ' : '' ?></h4>           
          </div>
          <div id="numberBody" class="modal-body table-responsive" style="overflow-y:auto;height:500px;">
            <div class="form-group">
              <div class="col-md-4">
                <select class="form-control" name="communication_type" onchange="change_cTypeAction()" id="communication_type">
                  <option value="sms">SMS</option>
                  <option value="notification">Notification</option>
                  <option value="notification_sms">Notification / SMS</option>
                  <?php if ($email_option) {
                    echo '<option value="email">Email</option>';
                  } ?>
                </select>
              </div>

              <div class="col-md-4" id="sms_notification"> 
                <div class="form-group">
                  <select class="form-control" onchange="change_template()" name="select_template" id="templateId">
                    <option value="">Select sms format</option>
                    <?php foreach ($sms_template as $key => $val) { ?>
                        <option value="<?php echo $val->id ?>"><?php echo $val->name ?></option>
                    <?php } ?>
                  </select>
                </div>
                <span class="help-block sms-format"></span>
              </div>

              <div class="col-md-4" id="emailshow" style="display: none;"> 
                <select class="form-control" onchange="change_email_template()" name="emailTemplate" id="emailTemplate">
                  <option value="">Select Template</option>
                  <?php foreach ($email_template as $key => $val) {
                    echo "<option value=".$val->id." >".$val->name."</option>";
                  } ?>
                </select>
                <span class="help-block"> <a href="javascript:void(0)" onclick="preview_email_content()">Preview Email Content</a></span>
              </div>

              <div class="col-md-4" id="emailhide"> 
                <select class="form-control" name="sent_to" id="sent_to" onchange="showPreferred()">
                  <option value="Both">Both</option>
                  <option value="Father">Father</option>
                  <option value="Mother">Mother</option>
                  <option value="preferred_parent">Preferred Parent</option>
                  <option value="preferred">Preferred Number</option>
                </select> 
              </div>

            </div>
            <table class="table table-bordered" width="100%">
              <thead>
                <tr>
                  <th width="15%">Student Name</th>
                  <th class="email_dispaly_only emailFather" style="display: none;">Father Name / Email</th>
                  <th class="email_dispaly_only eamilMother" style="display: none;">Mother Name / Email</th>
                  <th class="sms_dispaly_only">Message</th>
                </tr>
              </thead>
              <tbody id="dynamic-content">
              </tbody>
            </table>
            <div id="modal-loader-content" style="display: none; text-align: center;">
              <img src="<?php echo base_url('assets/img/ajax-loader.gif');?>" style="width:400px; height:400px;">
            </div>
          </div>
          <div class="modal-footer">
            <span class="text-danger" style="display: none;" id="credits-err">Not enough credits to send sms</span>
            <input type="button" onclick="checkCredits()" id="confirmBtn" disabled="" class="btn btn-info" value="Confirm">
            <button type="button" id="cancelModal" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
          </div>
      </div>
    </div>
</div>
</form>

<script type="text/javascript">
  function change_cTypeAction() {
    $('#sent_to').val('Both');
    $('.clearmessage').val('');
    $('#templateId').val('');
    var comuType = $('#communication_type').val();
    if (comuType == 'email') {
      $('#emailshow').show();
      $('.email_dispaly_only').show();
      $('#sms_notification').hide();

      $('select').children('option[value="preferred_parent"]').attr('disabled', true);
      $('select').children('option[value="preferred"]').attr('disabled', true);
    }else{
      $('#sms_notification').show();
      $('#emailshow').hide();
      $('.email_dispaly_only').hide();
    }
  }
  // $('#communication_type').on('change',function(){
  //   if (this.value == 'email') {
  //     $('#emailshow').show();
  //     $('.email_dispaly_only').show();
  //     $('.sms_dispaly_only').hide();
  //     $('#sms_notification').hide();
  //   }else{
  //     $('#sms_notification').show();
  //     $('#emailshow').hide();
  //     $('.email_dispaly_only').hide();
  //     $('.sms_dispaly_only').show();
  //   }
  // });

  function preview_email_content() {
    $('.email_content').html('');
    $('#email_model').modal('toggle');
    $('.emailTemplate_loading').show();
    var emailtemplateId = $('#emailTemplate').val();
    if (emailtemplateId=='') {
      $('.email_content').html('Select Template');
    }
    $.ajax({
      url: '<?php echo site_url('feesv2/reports_v2/get_fee_balance_email_content'); ?>',
      type: 'POST',
      data: {'emailtemplateId':emailtemplateId},
    })
    .done(function(data){
      if (data==0) {
        $('.email_content').html('');
        return false;
      }
      var reData = $.parseJSON(data);
      $('.email_content').html(construct_emails(reData));
      $('.emailTemplate_loading').hide();
    });
  }

  function construct_emails(reData) {

    var emailSubject = reData.email_subject;
    if (reData.email_subject == undefined)
      emailSubject = 'Email subject not added';

    var registered_email = reData.registered_email;
    if (reData.registered_email ==undefined) 
      registered_email = 'From email not assigned';
    var content = reData.content;
    var html = '';
    html +='<div class="form-group">';
    html +='<label class="col-md-3 control-label">From: </label>';
    html +='<div class="col-md-8">';
    html +='<p>'+registered_email+'</p>';
    html +='</div>';
    html +='</div>';

    html +='<div class="form-group">';
    html +='<label class="col-md-3 control-label">Subject: </label>';
    html +='<div class="col-md-8" style="margin-bottom:12px;">';
    html +='<p>'+emailSubject+'</p>';
    html +='</div>';
    html +='</div>';
    html +='<div class="form-group">';
     html +='<label class="col-md-3 control-label">Message: </label>';
    html +='<div class="col-md-8">';
    html +=''+content+'';
    html +='</div>';
    html +='</div>';

    return html;
  }

  var aMerge = [];
	$(document).ready(function(){
    var rawJson = '<?php echo addslashes(json_encode($student_names)); ?>';
    var s_names = JSON.parse(rawJson);
    console.log(s_names);
    for(var i=0; i < s_names.length; i++){
      aMerge.push(s_names[i].s_name.trim() + '('+s_names[i].class_name + ' '+ s_names[i].section_name+') ('+s_names[i].student_id+')');
    }
	});

	autocomplete(document.getElementById("student_name_fees"), aMerge);
	var merge = '';
	//console.log(aMerge);
	// alert(aMerge);
	function autocomplete(inp, arr) {
	  // alert(inp);
	     /*the autocomplete function takes two arguments,
	    the text field element and an array of possible autocompleted values:*/
	    var currentFocus;
	    /*execute a function when someone writes in the text field:*/
	    inp.addEventListener("input", function(e) {
	        var a, b, i, val = this.value;
	        /*close any already open lists of autocompleted values*/
	        closeAllLists();
	        if (!val) { $("#stakeholder_id").val(0); return false;}
	        currentFocus = -1;
	        /*create a DIV element that will contain the items (values):*/
	        a = document.createElement("DIV");
	        a.setAttribute("id", this.id + "autocomplete-list");
	        a.setAttribute("class", "autocomplete-items");
	        /*append the DIV element as a child of the autocomplete container:*/
	        this.parentNode.appendChild(a);
	        /*for each item in the array...*/
	        for (i = 0; i < arr.length; i++) {
	            /*check if the item starts with the same letters as the text field value:*/
	          if (arr[i].substr(0, val.length).toUpperCase() == val.toUpperCase()) {
	            /*create a DIV element for each matching element:*/
	            b = document.createElement("DIV");
	            b.style.cursor = 'pointer';
	            /*make the matching letters bold:*/
	            var stdNameSplit = arr[i].split('\(');

	            var merge = stdNameSplit[0];
	            var split1 = '('+stdNameSplit[1];
	            var std_stf_id = stdNameSplit[2]; 

	            var stakeholder_id = std_stf_id.split(')')[0];

	            b.innerHTML = "<strong>" + merge.substr(0, val.length) + "</strong>";
	            // b.innerHTML +=  arr[i].substr(val.length);
	            b.innerHTML += merge.substr(val.length) + ' ' + split1;
	            /*insert a input field that will hold the current array item's value:*/
	            b.innerHTML += "<input type='hidden' value='" +merge + " " + split1 + "_" +stakeholder_id+"'>";
	            /*execute a function when someone clicks on the item value (DIV element):*/
	                b.addEventListener("click", function(e) {
	                /*insert the value for the autocomplete text field:*/
	                inp.value = this.getElementsByTagName("input")[0].value;
	                sepstdName = this.getElementsByTagName("input")[0].value;
	               var nameSplit = sepstdName.split('_');
	               var stf_std_id = nameSplit[1];
	               $("#student_name_fees").val(nameSplit[0]);
	               var types = nameSplit[0].split('(');
	                // console.log(types)
	                var type = 'student';
	            	if(types[1] == 'staff) ') {
	            		type = 'staff';
	            	}
	            	$("#stakeholder_id").val(stf_std_id);
	                // get_communication_data(stf_std_id,type);
	                /*close the list of autocompleted values,
	                (or any other open lists of autocompleted values:*/
	                closeAllLists();
	            });
	            a.appendChild(b);
	          }
	        }
	    });
	    /*execute a function presses a key on the keyboard:*/
	    inp.addEventListener("keydown", function(e) {
	        var x = document.getElementById(this.id + "autocomplete-list");
	        if (x) x = x.getElementsByTagName("div");
	        if (e.keyCode == 40) {
	            /*If the arrow DOWN key is pressed,
	            increase the currentFocus variable:*/
	            currentFocus++;
	            /*and and make the current item more visible:*/
	            addActive(x);
	        } else if (e.keyCode == 38) { //up
	            /*If the arrow UP key is pressed,
	            decrease the currentFocus variable:*/
	            currentFocus--;
	            /*and and make the current item more visible:*/
	            addActive(x);
	        } else if (e.keyCode == 13) {
	            /*If the ENTER key is pressed, prevent the form from being submitted,*/
	            e.preventDefault();
	            if (currentFocus > -1) {
	            /*and simulate a click on the "active" item:*/
	            if (x) x[currentFocus].click();
	            }
	        }
	    });
	    function addActive(x) {
	        /*a function to classify an item as "active":*/
	        if (!x) return false;
	        /*start by removing the "active" class on all items:*/
	        removeActive(x);
	        if (currentFocus >= x.length) currentFocus = 0;
	        if (currentFocus < 0) currentFocus = (x.length - 1);
	        /*add class "autocomplete-active":*/
	        x[currentFocus].classList.add("autocomplete-active");
	    }
	    function removeActive(x) {
	        /*a function to remove the "active" class from all autocomplete items:*/
	      for (var i = 0; i < x.length; i++) {
	        x[i].classList.remove("autocomplete-active");
	      }
	    }
	    function closeAllLists(elmnt) {
	        /*close all autocomplete lists in the document,
	        except the one passed as an argument:*/
	      var x = document.getElementsByClassName("autocomplete-items");
	      for (var i = 0; i < x.length; i++) {
	        if (elmnt != x[i] && elmnt != inp) {
	        x[i].parentNode.removeChild(x[i]);
	        }
	      }
	    }
	    /*execute a function when someone clicks in the document:*/
	    document.addEventListener("click", function (e) {
	        closeAllLists(e.target);
	    });
	}

  $("#classId").change(function () {
    const previouslySelectedSections = $('#classSectionId').val();

    $('#classSectionId').html(''); 
    const selectedValue = $(this).val();

    $.ajax({
        url: '<?php echo site_url('feesv2/reports_v2/get_class_section_by_fees_selection_class') ?>',
        data: { 'feeclass': selectedValue },
        type: "post",
        success: function (data) {
            const resdata = JSON.parse(data);
            let option = '';
            resdata.forEach(item => {
                option += `<option value="${item.section_id}">${item.class_section}</option>`;
            });

            $("#classSectionId").html(option);
            $('#classSectionId').selectpicker('refresh');

            if (previouslySelectedSections) {
                $('#classSectionId').val(previouslySelectedSections).selectpicker('refresh');
            }
        },
        error: function (err) {
            console.log(err);
        }
    });
});
</script>

<div id="email_model" class="modal fade" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content" style="width: 60%; margin: auto;">
      <div class="modal-header">
        <h4 class="modal-title">Email Content details</h4>           
      </div>
      <div class="col-12 text-center emailTemplate_loading" style="display: none;">
        <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
      </div>
      <div  class="modal-body email_content" style="overflow-y:auto;height:500px;">
        
      </div>
      <div class="modal-footer">
        <button type="button" id="cancelModal" class="btn btn-secondary" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>