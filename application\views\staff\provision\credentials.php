<ul class="breadcrumb">
	<li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
	<li><a href="<?php echo site_url('SchoolAdmin_menu');?>">User Management</a></li>
	<li class="active">Staff Provision</li>
</ul>

<div class="col-md-12 col_new_padding">
	<div class="card cd_border">
		<div class="card-header panel_heading_new_style_staff_border">
			<div class="row" style="margin: 0px">
				<h3 class="card-title panel_title_new_style_staff">
					<a class="back_anchor" href="<?php echo site_url('SchoolAdmin_menu') ?>" class="control-primary">
						<span class="fa fa-arrow-left"></span>
					</a> 
					Provision Staff Credentials
				</h3>
			</div>
		</div>
		<div class="card-body pt-1">
			<?php if ($smsConfigured == 0 || $emailConfigured == 0) {?>
				<div class="col-md-12">
					<p class="text-danger" style="font-size: 14px;">
						<?php if($smsConfigured == 0 && $emailConfigured == 0) { ?>
							<strong>Note: </strong>
                            <strong>SMS and Email are not configured. To use either of these features, please contact the School Administrator or IT Team for further assistance.</strong>
						<?php } else if($emailConfigured == 0) { ?>
							<strong class="text-success">Currently, only SMS is in use.</strong>
                            <br>
                            <strong>Note: </strong>
                            <strong>Please contact the Administrator or IT Team to enable the Email feature if needed.</strong>
						<?php } else if($smsConfigured == 0) { ?>
							<strong class="text-success">Currently, only Email is in use.</strong>
                            <br>
                            <strong>Note: </strong>
                            <strong>Please contact the Administrator or IT Team to enable the SMS feature if needed.</strong>
						<?php } ?>
					</p>
				</div>
			<?php } else if($smsConfigured == 1 && $emailConfigured == 1) { ?>
				<div class="col-md-12">
					<p class="text-success" style="font-size: 14px;"> <strong>Note: </strong>
						<strong>SMS and Email Templates are configured</strong>
					</p>
				</div>
			<?php }?>
			<div class="col-md-4">
				<table class="table table-bordered">
					<thead>
						<tr>
							<th>Logged In</th>
							<th>Activated</th>
							<th>Not Activated</th>
							<th>Total</th>
						</tr>
					</thead>
					<tbody>
						<tr id="counts-row">
							<td id="count-loggedIn"><span class="counts-loader">...</span></td>
							<td id="count-activated"><span class="counts-loader">...</span></td>
							<td id="count-not-activated"><span class="counts-loader">...</span></td>
							<td id="count-total"><span class="counts-loader">...</span></td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>

		<div class="card-body pt-1">
			<div class="col-md-12 table-responsive" id="scroll_div">
				<!-- Loader -->
				<div id="staff-table-loader" style="display:none;text-align:center;" class="mb-3">
					<div class="no-data-display"><i class="fa fa-spin fa-spinner"></i> Loading...</div>
				</div>
				<table class="table table-striped" id="scroll_div_table">
					<thead>
						<tr>
							<th rowspan="2">#</th>
							<th rowspan="2">Name</th>
							<th rowspan="2">Username</th>
							<th rowspan="2">Mobile Number</th>
							<th rowspan="2">Email Id</th>
							<th rowspan="2" style="width:10%">
							<select style="width: 70px;" class="form-control method_typeChangefunction" id="onchangeSendMethod" name="send_method_type">
								<?php if($smsConfigured == 0 && $emailConfigured == 0) { ?>
									<option value="sms" disabled selected>SMS</option>
									<option value="email" disabled>Email</option>
								<?php } ?>
								<?php if($smsConfigured == 1 && $emailConfigured == 1) { ?>
									<option value="sms">SMS</option>
									<option value="email">Email</option>
								<?php } else if($smsConfigured == 1) { ?>
									<option value="sms" selected>SMS</option>
								<?php } else if($emailConfigured == 1) { ?>
									<option value="email" selected>Email</option>
								<?php }?>
							</select>
							</th>
							<th rowspan="2">Status</th>
							<?php if($smsConfigured == 0 && $emailConfigured == 0) { ?>
								<th>
									<input type="button" onclick="send_provision_credentials()" id="sendButton" disabled="true"  class="btn btn-primary" value="Send">
								</th>
								<th>
									<input type="button" onclick="re_send_provision_credentials()" id="re-sendButton" disabled="true"  class="btn btn-primary" value="Re-Send">
								</th>
							<?php } else { ?>
								<th>
									<input type="button" onclick="send_provision_credentials()" id="sendButton" disabled="true"  class="btn btn-primary" value="Send">
								</th>
								<th>
									<input type="button" onclick="re_send_provision_credentials()" id="re-sendButton" disabled="true"  class="btn btn-primary" value="Re-Send">
								</th>
							<?php }?>
							<th>
								<input type="button" onclick="deactivate_provision_credentials()" id="deactivateButton" disabled="true"  class="btn btn-primary" value="Deactivate">
							</th>
							<th rowspan="2">Reset password</th>
						</tr>
						<tr>
							<?php if($smsConfigured == 0 && $emailConfigured == 0) { ?>
								<th>Send
									<input type="checkbox" name="selectAll" value="send" onclick="check_all(this, value)" id="sendAll" class="check" disabled>
								</th>
								<th>Re-Send
									<input type="checkbox" name="selectAll" value="re_send" onclick="check_all(this, value)" id="re_sendAll" class="check" disabled>
								</th>
							<?php } else { ?>
								<th>Send
									<input type="checkbox" name="selectAll" value="send" onclick="check_all(this, value)" id="sendAll" class="check">
								</th>
								<th>Re-Send
									<input type="checkbox" name="selectAll" value="re_send" onclick="check_all(this, value)" id="re_sendAll" class="check">
								</th>
							<?php }?>
							<th>Deactivate
								<input type="checkbox" name="selectAll" value="deactivate" onclick="check_all(this, value)" id="deactivateAll" class="check">
							</th>
						</tr>
					</thead>
					<tbody id="staff-table-body">
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>

<div id="summary2" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
	<div class="modal-dialog modal-dialog-scrollable" role="document">
		<div class="modal-content" style="width: 80%;margin-top: 1% !important;margin:auto;">
			<div class="modal-header">
				<h4 class="modal-title">Review the number(s) / email(s) and provide confirmation.</h4>
				<button type="button" class="close" data-dismiss="modal">&times;</button>
			</div>
			<div id="numberBody2" class="modal-body table-responsive" style="overflow-y:auto;height:500px;">
				<form enctype="multipart/form-data" method="post" id="staff" class="form-horizontal">
					<div id="modal-loader" style="display: none; text-align: center;">
						<div class="no-data-display">Please wait while the data is being prepared...</div>
						<img src="<?php echo base_url('assets/img/ajax-loader.gif');?>" style="width:400px; height:400px;">
					</div>
					<table class="table table-bordered" id="dynamic-content2" width="100%">
					</table>
				</form>
			</div>
			<div class="modal-footer">
				<span id="credit-err" class="text-danger">Not enough credits to send sms</span>
				<button type="button" id="cancelModal" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
				<button type="button" id="confirmBtn" onclick="form_submit(this)" class="btn btn-primary mt-0">Confirm</button>
			</div>
		</div>
	</div>
</div>

<style>
	.dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}

	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript">

	$(document).ready(function() {
		setTimeout(() => {
			$('#scroll_div_table').DataTable( {
				"language": {
					"search": "",
					"searchPlaceholder": "Enter Search..."
				},
				"lengthMenu": [ [-1], ["All"] ],
				"pageLength": -1,
				dom: 'lBfrtip',
				// scrollY: 300,
				// scrollX: true,
				scrollCollapse: true,
					"ordering": false,
					buttons: [
						{
							extend: 'excelHtml5',
							text: 'Excel',
							filename: 'staff_credential',
							className: 'btn btn-info btn_fixed1',
							exportOptions: {
								columns: [0, 1, 2, 3, 4, 6] // Specify the column indices you want to export
							}
						}
					]
			} );
		}, 1500);
	});


	$(document).ready(function() {
		add_scroller('scroll_div');
	});

    function check_all(check, value){
        if (value == 'send') {
            isCheckedBySend(check)
        }
        if(value == 're_send'){
            isCheckedByReSend(check)
        }

        if(value == 'deactivate'){
            isCheckedByDeactivate(check)
        }
    }

    function isCheckedBySend(check) {
		if($(check).is(':checked')) {
			$('.sendCheck').prop('checked',true);
			$('#sendButton').prop('disabled',false);
			$('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop({"disabled": "true", "checked": ""});
		}else{
			$('.sendCheck').prop('checked',false);
			$('#sendButton').prop('disabled',true);
			$('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled',false);
		}
    }

    function isCheckedByReSend(check) {
        if($(check).is(':checked')) {
            $('.re_sendCheck').prop('checked',true);
            $('#re-sendButton').prop('disabled',false);
            $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll').prop({"disabled": "true", "checked": ""});
        }else{
            $('.re_sendCheck').prop('checked',false);
            $('#re-sendButton').prop('disabled',true);
            $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled',false);
        }
    }

    function isCheckedByDeactivate(check) {
        if($(check).is(':checked')) {
            $('.deactivate_sendCheck').prop('checked',true);
            $('#deactivateButton').prop('disabled',false);
            $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll').prop({"disabled": "true", "checked": ""});
        }else{
            $('.deactivate_sendCheck').prop('checked',false);
            $('#deactivateButton').prop('disabled',true);
			<?php if($smsConfigured == 0 && $emailConfigured == 0) { ?>
				$('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll').prop('disabled',true);
			<?php } else { ?>
				$('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll').prop('disabled',false);
			<?php }?>
        }
    }
    $('.method_typeChangefunction').on('change',function(){
		var value = $(this).val();
		// Set all .method_type_dropdown selects to the same value
		$('.method_type_dropdown').each(function () {
			$(this).val(value); // this sets the selected option correctly
		});
    });

    function check_smsIndividual() {
        if ($("input[class='sendCheck']:checked").length > 0) {
            $('#sendButton').prop('disabled',false);
            $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop({"disabled": "true", "checked": ""});
        }else{
            $('.sendCheck').prop('checked',false);
            $('#sendButton').prop('disabled',true);
            $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled',false);
        }
    }

    function check_reSMSIndividual() {
        if ($("input[class='re_sendCheck']:checked").length > 0) {
            $('#re-sendButton').prop('disabled',false);
            $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll').prop({"disabled": "true", "checked": ""});
        }else{
            $('.re_sendCheck').prop('checked',false);
            $('#re-sendButton').prop('disabled',true);
            $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled',false);
        }
    }

    function check_deactivateIndividual() {
        if ($("input[class='deactivate_sendCheck']:checked").length > 0) {
            $('#deactivateButton').prop('disabled',false);
            $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll').prop({"disabled": "true", "checked": ""});
        }else{
            $('.deactivate_sendCheck').prop('checked',false);
            $('#deactivateButton').prop('disabled',true);
			<?php if($smsConfigured == 0 && $emailConfigured == 0) { ?>
				$('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll').prop('disabled',true);
			<?php } else { ?>
				$('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll').prop('disabled',false);
			<?php }?>
        }
    }

	function send_provision_credentials() {
        var staffIds = [];
        var smsType = [];
        $('.sendCheck:checked').each(function() {
            staffIds.push($(this).val()+'_'+$('#method_type_indiv_'+$(this).val()).val());

        });
        if (staffIds.length <=0) {
            return false;
        }
		$("#credit-err").hide();
        $('#summary2').modal('show');
		$("#confirmBtn").attr('disabled', true);
        sendActiveLink(staffIds, 'activation');
    }

    function re_send_provision_credentials() {
        var staffIds = [];
        var smsType = [];
        $('.re_sendCheck:checked').each(function() {
            staffIds.push($(this).val()+'_'+$('#method_type_indiv_'+$(this).val()).val());

        });
        if (staffIds.length <=0) {
            return false;
        }
		$("#credit-err").hide();
        $('#summary2').modal('show');
		$("#confirmBtn").attr('disabled', true);
        sendActiveLink(staffIds, 'activation');
    }

	function form_submit(btn) {
		$(btn).text('Please wait...').prop('disabled', true);

		const form = $('#staff')[0];
		const formData = new FormData(form);

		$.ajax({
			url: '<?php echo site_url('staff/staff_controller/send_sms_email_active_staff'); ?>',
			type: 'POST',
			data: formData,
			processData: false,
			contentType: false,
			success: function (data) {
				$(btn).text('Confirm').prop('disabled', false);
				let response = JSON.parse(data);
				if (response.status == 'success') {
					Swal.fire({
						icon: 'success',
						title: 'Success',
						text: response.message,
						confirmButtonText: 'OK'
					}).then(() => {
						$('#summary2').modal('hide');
						fetchStaffDataAndCounts(); // Refresh the page or table
					});
				} else {
					Swal.fire({
						icon: 'error',
						title: 'Error',
						text: response.message,
						confirmButtonText: 'OK'
					});
				}
			},
			error: function () {
				$(btn).text('Confirm').prop('disabled', false);
				Swal.fire({
					icon: 'error',
					title: 'Unexpected Error',
					text: 'Something went wrong. Please try again.',
					confirmButtonText: 'OK'
				});
			}
		});
	}

	function escapeHtml(str) {
		return String(str)
			.replace(/&/g, "&amp;")
			.replace(/"/g, "&quot;")
			.replace(/'/g, "&#039;")
			.replace(/</g, "&lt;")
			.replace(/>/g, "&gt;");
	}

    function sendActiveLink(staffIds) {
		$('#modal-loader').show();
		$('#dynamic-content2').html('');
		if(staffIds.length > 0){
			$.ajax({
				url: '<?php echo site_url('staff/staff_controller/getstaff_activation_preview'); ?>',
				type: 'POST',
				data: {'staffIds':staffIds},
				dataType: 'html'
			})
			.done(function(data){
				var data = $.parseJSON(data);
				if(data.config){
					$('#modal-loader').hide();
					$("#credit-err").hide();
					$('#dynamic-content2').html(`<div class="no-data-display">${data.config}</div>`);
					return;
				}
				var previewData = data.preview;
				var credits_available = data.credits_available;
				var html = '';
				let hasData = false;
				if(previewData.length == 0) {
					html += '<h4>No data</h4>';
				} else {
					html += '<thead><tr><th>#</th><th>Name</th><th>Number/Email</th><th>Message</th></tr></thead><tbody>';
					let slno = 0;
					for (var i = 0; i < previewData.length; i++) {
						if(previewData[i].message_by == null || previewData[i].message_by.trim() == ''){
							continue;
						}
						var escapedMessage = previewData[i].message.replace(/"/g, '&quot;');
						hasData = true;
						html += '<tr>';
						html += '<td>'+(slno+1)+'</td>';
						html += '<td>'+previewData[i].name+'</td>';
						html += '<td>'+(previewData[i].message_by ? previewData[i].message_by : '-')+'</td>';
						html += `<td>
							${previewData[i].message}
							<input type="hidden" name="messages[${previewData[i].staffId}_${previewData[i].userId}_${previewData[i].send_type}]" value="${escapedMessage}">
							<input type="hidden" name="numbers[${previewData[i].staffId}]" value="${previewData[i].number}">
						</td>`;
						html += '</tr>';
						slno++;
					}
					if(!hasData){
                        html += '<tr><td colspan="4" class="text-center">No Contact Details Found For The Selected Users</td></tr>';
                        $("#confirmBtn").attr('disabled', true);
                    }
					html += '</tbody></table>';
				}
				$('#dynamic-content2').html(html); // load here
				$('#modal-loader').hide(); // hide loader
				if (credits_available == 1) {
                    //enable confirm button only if credits available
					if(hasData){
                        $("#confirmBtn").attr('disabled', false);
                    }
                    $("#credit-err").hide();
                } else {
                    $("#confirmBtn").attr('disabled', true);
                    $("#credit-err").show();
                }
			})
			.fail(function(){
				$('#dynamic-content2').html('<i class="glyphicon glyphicon-info-sign"></i> Something went wrong, Please try again...');
				$('#modal-loader').hide();
				$("#confirmBtn").hide();
			});
		} else {
			$('#modal-loader').hide();
			$("#confirmBtn").hide();
			$('#dynamic-content2').html('<i class="glyphicon glyphicon-info-sign"></i> Please select staffs...');
		}
	}

    function deactivate_provision_credentials() {
        var userIds = [];
		$('.deactivate_sendCheck:checked').each(function () {
			userIds.push($(this).val());
		});

		if (userIds.length <= 0) {
			Swal.fire({
				icon: 'warning',
				title: 'No Users Selected',
				text: 'Please select at least one user to deactivate.'
			});
			return false;
		}

        Swal.fire({
			title: 'Deactivating Users',
			text: `You are deactivating ${userIds.length} user(s). Are you sure?`,
			icon: 'warning',
			showCancelButton: true,
			confirmButtonText: 'Yes',
			cancelButtonText: 'No',
			reverseButtons: true,
		}).then((result) => {
			if (result.isConfirmed) {
				$.ajax({
					url: '<?php echo site_url('staff/staff_controller/deactivate_staff_provision_credentials_by_user_id'); ?>',
					type: 'POST',
					data: { userIds: userIds },
					success: function (response) {
						let data = JSON.parse(response);
						if (data) {
							Swal.fire({
								icon: 'success',
								title: 'Success',
								text: 'Users deactivated successfully!',
								timer: 1500,
								showConfirmButton: false
							}).then(() => {
								fetchStaffDataAndCounts();
							});
						} else {
							Swal.fire('Error', 'Something went wrong.', 'error');
						}
					},
					error: function () {
						Swal.fire('Error', 'Something went wrong.', 'error');
					}
				});
			}
		});
    }

function reset_staff_password(userId, username, staffId) {
    Swal.fire({
        title: 'Reset to Default Password',
        html: `<b>User name:</b> ${username}<br><b>Password:</b> welcome123`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Yes',
        cancelButtonText: 'No',
        reverseButtons: true,
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo site_url('staff/staff_controller/reset_staff_default_password_user_id'); ?>',
                type: 'POST',
                data: { userId: userId },
                success: function (response) {
                    let data = JSON.parse(response);
                    if (data) {
                        Swal.fire('Success', 'Password reset successfully.', 'success');
                        $(`#reset_password_${staffId}`).replaceWith('<span>Refresh to view changes</span>');
                    } else {
                        Swal.fire('Error', 'Failed to reset password.', 'error');
                    }
                },
                error: function () {
                    Swal.fire('Error', 'Something went wrong.', 'error');
                }
            });
        }
    });
}

function fetchStaffDataAndCounts() {
    // Show loaders
    $('#count-loggedIn, #count-activated, #count-not-activated, #count-total').html('<span class="counts-loader">Loading...</span>');
    $('#staff-table-loader').show();
    $('#staff-table-body').html('');
	$('#sendButton').prop('disabled', true);
	$('#re-sendButton').prop('disabled', true);
	$('#deactivateButton').prop('disabled', true);
	let smsConfigured = <?php echo $smsConfigured; ?>;
	let emailConfigured = <?php echo $emailConfigured; ?>;
    $.ajax({
        url: '<?php echo site_url('staff/Staff_controller/get_provision_staff_data_and_counts'); ?>',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            // Update counts
            var counts = response.counts;
            $('#count-loggedIn').text(counts.loggedIn);
            $('#count-activated').text(counts.activated);
            $('#count-not-activated').text(counts.not_activated);
            $('#count-total').text(counts.total);

            // Render staff table
            var staffs = response.staffs;
            var html = '';
            var i = 1;
            staffs.forEach(function(staff) {
                var activeDisabled = staff.active == 1 ? 1 : 0;
                var loggedStatus = '';
                if (staff.loggedin_atleast_once == 1) {
                    loggedStatus = '<button type="button" class="btn btn-info btn-xs">Logged-in</button>';
                } else if (staff.active == 1) {
                    loggedStatus = '<button type="button" class="btn btn-warning btn-xs">Activated</button>';
                } else {
                    loggedStatus = '<button type="button" class="btn btn-danger btn-xs">Not activated</button>';
                }

                html += `<tr id="staff_row_${staff.staffId}">`;
                html += '<td>' + (i++) + '</td>';
                html += '<td>' + staff.staffName + '</td>';
                html += '<td>' + staff.username + '</td>';
                html += '<td>' + (staff.contact_number ? staff.contact_number : 'NA') + '</td>';
                html += '<td>' + (staff.email ? staff.email : 'NA') + '</td>';
				if(smsConfigured == 0 && emailConfigured == 0) {
					html += '<td><select class="form-control method_type_dropdown" name="select_send_options" id="method_type_indiv_' + staff.staffId + '"><option value="sms" disabled selected>SMS</option><option value="email" disabled>Email</option></select></td>';
				} else if(smsConfigured == 0) {
					html += '<td><select class="form-control method_type_dropdown" name="select_send_options" id="method_type_indiv_' + staff.staffId + '"><option value="email" selected>Email</option></select></td>';
				} else if(emailConfigured == 0) {
					html += '<td><select class="form-control method_type_dropdown" name="select_send_options" id="method_type_indiv_' + staff.staffId + '"><option value="sms" selected>SMS</option></select></td>';
				} else {
					html += '<td><select class="form-control method_type_dropdown" name="select_send_options" id="method_type_indiv_' + staff.staffId + '"><option value="sms">SMS</option><option value="email">Email</option></select></td>';
				}
                html += '<td>' + loggedStatus + '</td>';

                // Send
                if (activeDisabled) {
                    html += (staff.loggedin_atleast_once == 1) ? '<td><font color="green">Logged-in</font></td>' :
                    '<td><font color="orange">Activated</font></td>';
                } else {
					if(smsConfigured == 0 && emailConfigured == 0) {
						html += '<td>-</td>';
					} else {
						html += '<td><input type="checkbox" onclick="check_smsIndividual()" name="send_credentials" value="' + staff.staffId + '" class="sendCheck"></td>';
					}
                }

                // Re-Send
                if (activeDisabled) {
					if(smsConfigured == 0 && emailConfigured == 0) {
						html += '<td>-</td>';
					} else {
						html += '<td><input type="checkbox" onclick="check_reSMSIndividual()" name="re_send_credentials" value="' + staff.staffId + '" class="re_sendCheck"></td>';
					}
                } else {
                    html += '<td>Not Activated</td>';
                }

                // Deactivate
				if (activeDisabled) {
                    html += '<td><input type="checkbox" onclick="check_deactivateIndividual()" name="deactivate_credentials" value="' + staff.userId + '" class="deactivate_sendCheck"></td>';
                } else {
                    html += '<td>Not Activated</td>';
                }

                // Reset password
                if (activeDisabled) {
                    html += `<td><input type="button" onclick="reset_staff_password(${staff.userId}, '${staff.username}', ${staff.staffId})" id="reset_password_${staff.staffId}" value="Reset to default" class="btn btn-primary"></td>`;
                } else {
                    html += '<td>Not Activated</td>';
                }

                html += '</tr>';
            });
            $('#staff-table-body').html(html);
            $('#staff-table-loader').hide();
        },
        error: function() {
            $('#count-loggedIn, #count-activated, #count-not-activated, #count-total').text('Error');
            $('#staff-table-body').html('<tr><td colspan="11">Failed to load data.</td></tr>');
            $('#staff-table-loader').hide();
        }
    });
}

$(document).ready(function() {
    fetchStaffDataAndCounts();
});
</script>