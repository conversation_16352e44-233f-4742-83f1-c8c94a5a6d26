<?php

class Invoice_model_v2 extends CI_Model{
    private $yearId;
    public function __construct(){
        parent::__construct();
        $this->yearId = $this->acad_year->getAcadYearID();
    }

    public function get_invoice_details($salesYearId){
        if($salesYearId > 0) {
            $sales_year_id= $salesYearId;
        } else if($salesYearId == '0') {
            $sales_year_id= $this->db_readonly->select('id')->where('is_active', 1)->get('procurement_sales_year')->row();
            if(!empty($sales_year_id)) {
                $sales_year_id= $sales_year_id->id;
            } else {
                $sales_year_id= '';
            }
        } else {
            $sales_year_id= '';
        }
       $this->db_readonly->select('im.id, vm.vendor_name, im.invoice_no, im.bill_no, im.total_amount, im.status,im.created_on,a.friendly_name, im.dc_type');
        $this->db_readonly->from('procurement_delivery_challan_master im');
        $this->db_readonly->join('procurement_vendor_master vm', 'im.vendor_id=vm.id');
        $this->db_readonly->join('avatar a','a.id=im.created_by');
        if($sales_year_id && $sales_year_id != '' && !empty($sales_year_id)) {
            $this->db_readonly->where('im.sales_year_id',$sales_year_id);
        }
        $data= $this->db_readonly->get()->result();
        // join('staff_master sm','sm.id=a.stakeholder_id')

        // echo '<pre>'; print_r($this->db_readonly->last_query($data)); die();

        if(!empty($data)) {
            return $data;
        }
        return array();
    }

    public function getInvoiceById($id) {
        $this->db->select("im.*, vm.vendor_name,pi.*, date_format(im.delivery_note_date, '%d-%m-%Y') as delivery_note_date_1, date_format(im.invoice_date, '%d-%m-%Y') as invoice_date_1, date_format(im.order_date, '%d-%m-%Y') as order_date_1, im.id as invoice_master_id");
        $this->db->from('procurement_delivery_challan_master im');
        $this->db->join('procurement_vendor_master vm', 'im.vendor_id=vm.id');
        $this->db->join('procurement_vendor_payment_instruments pi', 'im.payment_instrument_id=pi.id', 'left');
        $this->db->where('im.id', $id);
        $data= $this->db->get()->row();
        if(!empty($data)) {
            return $data;
        }
        return array();
    }

    public function getItemsId($id){ 
        $this->db_readonly->select("ii.id as invoice_items_id, ii.initial_quantity as quantity1, ii.*,pv.item_name as variantName, ( ifnull(price, 0) + ifnull(price, 0) * (ifnull(cGST, 0) / 100) +ifnull(price, 0) * (ifnull(sGST, 0) / 100) ) as new_unit_price");
        $this->db_readonly->from('procurement_delivery_challan_items ii');
        $this->db_readonly->join('procurement_itemmaster_items pv', 'ii.proc_im_items_id=pv.id');
        $this->db_readonly->where('ii.invoice_master_id', $id);
        $data= $this->db_readonly->get()->result();

        
        if(!empty($data)) {
            foreach($data as $key => $val) {
                $retQty= $this->db_readonly->select("ifnull(sum(return_quantity), 0) as qty")->where('proc_invoice_items_id', $val->invoice_items_id)->get('procurement_sales_return')->row();
                
                $soldQty= $this->db_readonly->select("ifnull(sum(quantity), 0) as qty")->where('proc_invoice_items_id', $val->invoice_items_id)->get('procurement_sales_transactions')->row();
                
                $alloQty= $this->db_readonly->select("ifnull(sum(allocated_quantity), 0) as qty")->where('proc_invoice_items_id', $val->invoice_items_id)->get('procurement_item_allocations_staff')->row();
                
                $collQty= $this->db_readonly->select("ifnull(sum(returned_quantity), 0) as qty")->join('procurement_item_allocations_staff pias', 'pias.id = pics.item_allocations_staff_id')->where('proc_invoice_items_id', $val->invoice_items_id)->get('procurement_item_collections_staff pics')->row();
                $softdelQty= $this->db_readonly->select("ifnull(sum(quantity), 0) as qty")->join('procurement_sales_master psm', 'psm.id = pst.sales_master_id')->where('pst.proc_invoice_items_id', $val->invoice_items_id)->where('soft_delete', 1)->get('procurement_sales_transactions pst')->row();
                $vendorretQty= $this->db_readonly->select("ifnull(sum(return_quantity), 0) as qty")->where('proc_invoice_items_id', $val->invoice_items_id)->get('procurement_delivery_challan_return_items')->row();

                $sold= $soldQty->qty - $retQty->qty - $softdelQty->qty;
                $allocated= $alloQty->qty - $collQty->qty;
                $string= "Sold= $sold <br>Allocated= $allocated <br>Vendor Returned= $vendorretQty->qty";
                $val->utilization= $string;
            }
        }
        return $data;
    }

    public function get_active_sales_year() {
        return $this->db_readonly->where('is_active', 1)->get('procurement_sales_year')->row();
    }

    public function addInvoice(){
        $input = $this->input->post();
        $sales_year_id= isset($input['sales_year_id']) ? $input['sales_year_id'] : 1;
        $variants = $input['variants'];

        $data = array(
            'vendor_id' => $input['vendor'],
            'invoice_no' => ($input['invoice_no'])?$input['invoice_no']:null,
            'bill_no' => ($input['bill_no'])?$input['bill_no']:null,
            'invoice_date' => ($input['invoice_date'])?date('Y-m-d', strtotime($input['invoice_date'])):null,
            'delivery_note' => ($input['delivery_note'])?$input['delivery_note']:null,
            'mode_of_payment' => ($input['payment_mode'])?$input['payment_mode']:null,
            'supplier_ref_no' => ($input['supplier_ref_no'])?$input['supplier_ref_no']:null,
            'order_no' => ($input['order_no'])?$input['order_no']:null,
            'order_date' => ($input['order_date'])?date('Y-m-d', strtotime($input['order_date'])):null,
            'dispatch_doc_no' => ($input['dispatch_doc_no'])?$input['dispatch_doc_no']:null,
            'delivery_note_date' => ($input['delivery_note_date'])?date('Y-m-d', strtotime($input['delivery_note_date'])):null,
            'total_amount' => $input['grand_total'],
            'payment_instrument_id' => ($input['payment_ins'])?$input['payment_ins']:null,
            'dispatched_by' => ($input['dispatched_by'])?$input['dispatched_by']:null,
            'destination' => ($input['destination'])?$input['destination']:null,
            'status' => $input['status'],
            'created_by' => $this->authorization->getAvatarId(),
            'last_modified_by' => $this->authorization->getAvatarId(),
            'sales_year_id' => $sales_year_id,
            // 'proc_im_category_id' => $category_id
        );
        $check_vendor= $this->db->select('vendor_name')->where('id', $input['vendor'])->get('procurement_vendor_master')->row()->vendor_name;
        if($check_vendor == 'Initial Quantity') {
            $data['dc_type']= 'Opening Balance';
        }
       


        $this->db->trans_start();
        $this->db->insert('procurement_delivery_challan_master', $data);
        $invoiceId = $this->db->insert_id();
        
        $variantQuantityData = array();
        foreach ($variants as $i => $varId) {
            $get_cat_subcat= $this->db->select('pis.id as sub_cat_id, pis.proc_im_category_id')
                ->from('procurement_itemmaster_items pii')
                ->join('procurement_itemmaster_subcategory pis', 'pis.id= pii.proc_im_subcategory_id')
                ->where('pii.id', $varId)
                ->get()->row();
            $category_id= $get_cat_subcat->proc_im_category_id;
            $subcategory_id= $get_cat_subcat->sub_cat_id;

            // $is_sales_year_exist= $this->db->where('proc_im_category_id', $category_id)->where("(sales_year_id is not null and sales_year_id = $sales_year_id)")->get('procurement_delivery_challan_master')->row();
            // if(empty($is_sales_year_exist)) {
                
            // } else {
            //     $invoiceId= $is_sales_year_exist->id;
            // }
            $variantQuantityData[$varId] = $input['quantity'][$i];
            // Solution for: [IISB] | [2024-08-21 15:53:56] | unknown | number_format() expects parameter 1 to be float, string given (/home/<USER>/oxygenv2/application/models/procurement/Invoice_model_v2.php:116) | (Warning:2) | 10.11.142.167:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36
            $rate= isset($input['rate'][$i]) && !is_string($input['rate'][$i]) && $input['rate'][$i] != '' ? $input['rate'][$i] : 0;
            $cGST= isset($input['cGST'][$i]) && !is_string($input['cGST'][$i]) && $input['cGST'][$i] != '' ? $input['cGST'][$i] : 0;
            $sGST= isset($input['sGST'][$i]) && !is_string($input['sGST'][$i]) && $input['sGST'][$i] != '' ? $input['sGST'][$i] : 0;
            $items[] = array(
                'invoice_master_id' => $invoiceId,
                'proc_im_items_id' => $varId,
                'variant_type' => 'item',
                'description' => $input['description'][$i],
                // 'hsn_sac_no' => $input['hsn'][$i],
                // 'quantity' => $input['quantity'][$i],
                'unit' => null,
                'price' => $input['rate'][$i],
                'cgst' => ( number_format($rate, 2) * number_format($cGST, 2) ) / 100,
                'sgst' => ( number_format($rate, 2) * number_format($sGST, 2) ) / 100,
                'total_amount' => $input['total_amount'][$i],
                'proc_im_subcategory_id' => $subcategory_id,
                'proc_im_category_id' => $category_id,
                'selling_price' =>$input['selling_price'][$i],
                // 'sku_code' =>$input['sku_code'][$i],
                'initial_quantity' => $input['quantity'][$i],
                'current_quantity' => $input['quantity'][$i],
                // 'total_quantity' => $input['quantity'][$i]
            );
        }
       
        $status = $this->_updateVariantQuantity($variants, $variantQuantityData);
        
        $this->db->insert_batch('procurement_delivery_challan_items', $items);
        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    private function _updateVariantQuantity($variant_ids, $quantity) {
        $result = $this->db->select("id, current_quantity, total_quantity")->where_in('id', $variant_ids)->get('procurement_itemmaster_items')->result();
        foreach ($result as $key => $value) {
            $data[] = array(
                'id' => $value->id,
                'current_quantity' => $value->current_quantity + $quantity[$value->id],
                'total_quantity' => $value->total_quantity + $quantity[$value->id]
            );
        }
        return $this->db->update_batch('procurement_itemmaster_items', $data, 'id');
    }

    public function getVendorProducts($vendor_id) {
        return $this->db->select('id, subcategory_name as product_name')
        ->from('procurement_itemmaster_subcategory ipm')
        ->where("proc_im_category_id in (select proc_im_category_id from procurement_vendor_category where vendor_id=$vendor_id)")
        ->get()->result();
    }

    public function getInvoiceData($salesYearId) {
        if($salesYearId > 0) {
            $sales_year_id= $salesYearId;
        } else if($salesYearId == '0') {
            $sales_year_id= $this->db_readonly->select('id')->where('is_active', 1)->get('procurement_sales_year')->row();
            if(!empty($sales_year_id)) {
                $sales_year_id= $sales_year_id->id;
            } else {
                $sales_year_id= '';
            }
        } else {
            $sales_year_id= '';
        }

        $this->db_readonly->select("im.id as invoice_id, im.invoice_no, im.total_amount as invoiceTotal, item.initial_quantity as quantity, item.total_amount as itemTotal, pv.item_name as variantName, vm.vendor_name, DATE_FORMAT(im.created_on, '%d-%m-%Y') as invoiceDate,im.created_on,a.friendly_name")
        ->from('procurement_delivery_challan_master im')
        ->join('procurement_delivery_challan_items item', 'item.invoice_master_id=im.id')
        ->join('procurement_vendor_master vm', 'vm.id=im.vendor_id')
        ->join('procurement_itemmaster_items pv', 'pv.id=item.proc_im_items_id')
        ->join('avatar a','a.id=im.created_by');
        // ->join('staff_master sm','sm.id=a.stakeholder_id')
        // ->where('a.avatar_type',4)
        if($sales_year_id && $sales_year_id != '' && !empty($sales_year_id)) {
            $this->db_readonly->where('im.sales_year_id',$sales_year_id);
        }

        $data= $this->db_readonly->order_by('im.id', 'desc')
        ->get()->result();

        if(!empty($data)) {
            return $data;
        }
        return array();
    }
    public function delete_invoice(){
        $id=$_POST['id'];
        $this->db->where('invoice_master_id',$id);
        $this->db->delete('procurement_delivery_challan_items');
        $this->db->where('id',$id);
        return $this->db->delete('procurement_delivery_challan_master');
    }

    public function get_sales_year() {
        return $this->db_readonly->get('procurement_sales_year')->result();
    }

    public function onclick_replace_qty_OR_price() {
        $x= $this->input->post();
        if($x['update_type'] == 'price') { 
            $arr= array(
                'selling_price' => $x['input_val']
            );
        } else {
            $sold= intval($x['vals']) - intval($x['current_quantity']);
            $curr= intval($x['input_val']) - intval($sold);
            $arr= array(
                'initial_quantity' => $x['input_val'],
                'current_quantity' => $curr
            );
        }
        return $this->db->where('id', $x['invItemsId'])->update('procurement_delivery_challan_items', $arr);
    }

    function submit_vendor_return() {
        $input= $this->input->post();
        // echo '<pre>'; print_r($input); die();
        
        $invoice_master_id= $input['invoice_master_id'];
        $remarkreturn= $input['remarkreturn'];
        $return_amount= $input['return_amount'];
        $return_quantity= $input['return_quantity'];

        $return_amountAll= 0;
        foreach($return_amount as $key => $val) {
            $return_amountAll += $val;
        }
        // echo '<pre>'; print_r($invoice_master_id); die();
        $data_master= array(
            'proc_invoice_master_id' => $invoice_master_id,
            'total_return_price' => $return_amountAll,
            'remarks' => $remarkreturn,
            'created_by' => $this->authorization->getAvatarStakeHolderId()
        );
        
        $this->db->trans_start();
            $this->db->insert('procurement_delivery_challan_return_master', $data_master);

            $insert_id= $this->db->insert_id();
           
            if($insert_id) {
                foreach($return_quantity as $key => $val) {
                    $old_value= $this->db->select('current_quantity')->where('id', $key)->get('procurement_delivery_challan_items')->row();
                    if(!empty($old_value) && ($old_value->current_quantity - $val) >= 0) {
                        $insert_data[]= array(
                            'vendor_return_master_id' => $insert_id,
                            'proc_invoice_items_id' => $key,
                            'return_quantity' => $val,
                            'return_price' => $return_amount[$key]
                        );
                        $update_data[]= array(
                            'id' => $key,
                            'current_quantity' => ($old_value->current_quantity - $val)
                        );
                    }
                }

                if(!empty($insert_data)) {
                    $this->db->insert_batch('procurement_delivery_challan_return_items', $insert_data);
                }
                if(!empty($update_data)) {
                    $this->db->update_batch('procurement_delivery_challan_items', $update_data, 'id');
                }
            }
        $this->db->trans_complete();
        if(!$this->db->trans_status()) {
            $this->db->trans_rollback();
        }
        return $this->db->trans_status();
    }

    function get_vendor_return_hisiory() {
        $sales_year_id= $this->input->post('sales_year_id');
        $result= $this->db_readonly->select("pvrm.id, if(pvrm.remarks is not null and pvrm.remarks != '', pvrm.remarks, '-') remarks, pvrm.total_return_price, pvrm.created_on, pvri.return_quantity as return_quantity, pvri.return_price as return_price, ifnull(pim.invoice_no, '-') as invoice_no, pvm.vendor_name, ifnull(pvm.vendor_code, '-') vendor_code, pii.item_name as item_name, ifnull(concat(sm.first_name, ' ', ifnull(sm.last_name, '')), 'Admin') as staff")
            ->from('procurement_delivery_challan_return_master pvrm')
            ->join('staff_master sm', 'sm.id = pvrm.created_by', 'left')
            ->join('procurement_delivery_challan_return_items pvri', 'pvri.vendor_return_master_id = pvrm.id')
            ->join('procurement_delivery_challan_master pim', 'pim.id = pvrm.proc_invoice_master_id')
            ->join('procurement_vendor_master pvm', 'pvm.id = pim.vendor_id')
            ->join('procurement_delivery_challan_items pInvI', 'pInvI.id = pvri.proc_invoice_items_id')
            ->join('procurement_itemmaster_items pii', 'pii.id = pInvI.proc_im_items_id')
            ->where('pim.sales_year_id', $sales_year_id)
            ->order_by('pvrm.id', 'desc')
            ->get()->result();

        return $result;
    }

    function getItemList() {
        return $this->db_readonly->select('id, item_name')->order_by('item_name')->get('procurement_itemmaster_items')->result();
    }

    function getItemWiseSalesYearWiseVendors() {
        $input= $this->input->post();
        $item_id= $input['item_id'];
        $salesYear= $input['salesYear'];

        $data= $this->db_readonly->select("pii.id as invoice_items_id, pim.id as invoice_master_id, pii.price, ifnull(pii.sgst, '-') as sgst, ifnull(pii.cgst, '-') as cgst, ifnull(pii.total_amount, 0) as total_amount, ifnull(pii.hsn_sac_no, '-') as hsn_sac_no, pii.current_quantity, pii.initial_quantity, ( ifnull(pii.price, 0) + ifnull(pii.price, 0) * (ifnull(cGST, 0) / 100) +ifnull(pii.price, 0) * (ifnull(sGST, 0) / 100) ) as calculated_unit_price, pvm.vendor_name, ifnull(pvm.vendor_code, '-') as vendor_code, ifnull(pim.invoice_no, '-') as invoice_no, ifnull(pim.bill_no, '-') as bill_no, pim.created_on, pim.dc_type")
            ->from('procurement_delivery_challan_master pim')
            ->join('procurement_vendor_master pvm', 'pim.vendor_id = pvm.id')
            ->join('procurement_delivery_challan_items pii', 'pii.invoice_master_id = pim.id')
            ->where('pii.proc_im_items_id', $item_id)
            ->where('pim.sales_year_id', $salesYear)
            // ->where_in('pim.dc_type', ['Normal Purchase', 'Opening Balance'])
            // ->group_by('pvm.id')
            ->get()->result();
        // echo '<pre>'; print_r($this->db_readonly->last_query()); die();
        return $data;
    }

    function submit_vendor_return_item_wise() {
        $input= $this->input->post();
        // echo '<pre>'; print_r($input); die();
        $invoice_master_ids= isset($input['invoice_master_id']) ? $input['invoice_master_id'] : array();
        $invoice_items_ids= isset($input['invoice_items_id']) ? $input['invoice_items_id'] : array();
        $return_quantitys= isset($input['return_quantity']) ? $input['return_quantity'] : array();
        $return_amounts= isset($input['return_amount']) ? $input['return_amount'] : array();
        $remarkreturn= isset($input['remarkreturn']) ? $input['remarkreturn'] : "";

       
        if(!empty($invoice_master_ids)) {
            $this->db->trans_start();
            foreach($invoice_master_ids as $key => $val) {
                $insert_return_master= array(
                    'proc_invoice_master_id' => $val,
                    'total_return_price' => $return_amounts[$key],
                    'remarks' => $remarkreturn,
                    'created_by' => $this->authorization->getAvatarStakeHolderId()
                );
                $this->db->insert('procurement_delivery_challan_return_master', $insert_return_master);
                $insert_id= $this->db->insert_id();
                if($insert_id) {
                    $old_value= $this->db->select('current_quantity')->where('id', $invoice_items_ids[$key])->get('procurement_delivery_challan_items')->row();
                    if(!empty($old_value) && ($old_value->current_quantity - $return_quantitys[$key]) >= 0) {
                        $insert_return_items= array(
                            'vendor_return_master_id' => $insert_id,
                            'proc_invoice_items_id' => $invoice_items_ids[$key],
                            'return_quantity' => $return_quantitys[$key],
                            'return_price' => $return_amounts[$key]
                        );
                        $update_invoice_items= array(
                            'current_quantity' => ($old_value->current_quantity - $return_quantitys[$key])
                        );

                        if(!empty($insert_return_items)) {
                            $this->db->insert('procurement_delivery_challan_return_items', $insert_return_items);
                        }
                        if(!empty($update_invoice_items)) {
                            $this->db->where('id', $invoice_items_ids[$key])->update('procurement_delivery_challan_items', $update_invoice_items);
                        }
                    }
                }
            }
            $this->db->trans_complete();
        } else {
            return false;
        }
       
        if(! $this->db->trans_status()) {
            $this->db->trans_rollback();
        }
        return $this->db->trans_status();
    }

    function get_active_POs() {
        $this->db_readonly->select('id,request_number, date_format(created_on, "%d-%m-%Y") as created_on, ifnull(po_name, "-") as po_name')
        ->from('procurement_requisition')
        ->where_not_in('LOWER(request_type)', ['service', 'services'])
        ->where_in('LOWER(status)', ['approved','sent to vendor','partially delivered', 'not delivered'])
        ->where('is_active', 1)
        ->where('LOWER(delivery_status) !=', 'delivered')
        ->order_by('id', 'desc');
        return $this->db_readonly->get()->result();
    }

    function get_po_data($PO_id) {
        $PO= $this->db_readonly->select("pr.vendor_id, pic.is_sellable, pii.item_name, pri.procurement_requisition_id, pri.id as procurement_requisition_items_id, pri.proc_im_items_id, pri.proc_im_subcategory_id, pri.proc_im_category_id, pri.item_quantity, pri.rate_per_unit, pri.sgst_per, pri.cgst_per, pri.total_item_amt_with_gst, pri.delivered_quantity, ifnull(pr.tolerance, 0) as tolerance")
        ->from('procurement_requisition pr')
        ->join('procurement_requisition_items pri', 'pri.procurement_requisition_id=pr.id')
        ->join('procurement_itemmaster_items pii', 'pii.id=pri.proc_im_items_id')

        ->join('procurement_itemmaster_subcategory pis', 'pis.id=pii.proc_im_subcategory_id')
        ->join('procurement_itemmaster_category pic', 'pic.id=pis.proc_im_category_id')

        ->where('pr.id', $PO_id)
        ->where('LOWER(pri.delivery_status) !=', 'delivered')
        ->get()->result();

        return $PO;

        // echo '<pre>'; print_r($PO); die();
    }

    public function addInvoicePO(){
        $input = $this->input->post();
        $sales_year_id= isset($input['sales_year_id']) ? $input['sales_year_id'] : 1;

        $running_number= $this->db->select('max(id) as maxId')->get('procurement_delivery_challan_master')->row();
        $max_num= 1;
        if(!empty($running_number) && $running_number->maxId > 0) {
            $max_num= $running_number->maxId + 1;
        }
        $school_short_name= $this->settings->getSetting('school_short_name');
        $school_short_name= strtoupper($school_short_name);
        $acadYearId= $this->yearId;
        $assessment_year= "20$acadYearId-" . (intval($acadYearId) + 1);
        $delivery_challan_note_number= "DC-$school_short_name-$assessment_year-$max_num";
        $variants = $input['variants'];

        // echo '<pre>'; print_r($input); die();

        $data = array(
            'vendor_id' => $input['vendor'],
            'invoice_no' => NULL,
            'bill_no' => ($input['bill_no'])?$input['bill_no']:NULL,
            'invoice_date' => NULL,
            'delivery_note' => ($input['delivery_note'])?$input['delivery_note']:NULL,
            'mode_of_payment' => ($input['payment_mode'])?$input['payment_mode']:NULL,
            'supplier_ref_no' => ($input['supplier_ref_no'])?$input['supplier_ref_no']:NULL,
            'order_no' => ($input['order_no'])?$input['order_no']:NULL,
            'order_date' => ($input['order_date'])?date('Y-m-d', strtotime($input['order_date'])):NULL,
            'dispatch_doc_no' => ($input['dispatch_doc_no'])?$input['dispatch_doc_no']:NULL,
            'delivery_note_date' => ($input['delivery_note_date'])?date('Y-m-d', strtotime($input['delivery_note_date'])):NULL,
            'total_amount' => $input['grand_total'],
            'payment_instrument_id' => NULL,
            'dispatched_by' => ($input['dispatched_by'])?$input['dispatched_by']:NULL,
            'destination' => ($input['destination'])?$input['destination']:NULL,
            'status' => $input['status'],
            'created_by' => $this->authorization->getAvatarId(),
            'last_modified_by' => $this->authorization->getAvatarId(),
            'sales_year_id' => $sales_year_id,
            'delivery_challan_note_number' => $delivery_challan_note_number,
            'dc_type' => 'PO',
            'procurement_requisition_id' => $input['PO_id'],
        );
       


        $this->db->trans_start();
        $this->db->insert('procurement_delivery_challan_master', $data);
        $invoiceId = $this->db->insert_id();
        
        $variantQuantityData = array();
        foreach ($variants as $i => $varId) {
            $category_id= $input['proc_im_category_id_arr'][$i];
            $subcategory_id= $input['proc_im_subcategory_id_arr'][$i];
            $variantQuantityData[$varId] = $input['quantity'][$i];
            $rate= isset($input['rate'][$i]) && !is_string($input['rate'][$i]) && $input['rate'][$i] != '' ? $input['rate'][$i] : 0;
            $cGST= isset($input['cGST'][$i]) && !is_string($input['cGST'][$i]) && $input['cGST'][$i] != '' ? $input['cGST'][$i] : 0;
            $sGST= isset($input['sGST'][$i]) && !is_string($input['sGST'][$i]) && $input['sGST'][$i] != '' ? $input['sGST'][$i] : 0;
            
            $items[] = array(
                'invoice_master_id' => $invoiceId,
                'proc_im_items_id' => $varId,
                'variant_type' => 'item',
                'description' => $input['description'][$i],
                'unit' => NULL,
                'price' => $input['rate'][$i],
                'cgst' => ( number_format($rate, 2) * number_format($cGST, 2) ) / 100,
                'sgst' => ( number_format($rate, 2) * number_format($sGST, 2) ) / 100,
                'total_amount' => $input['total_amount'][$i],
                'proc_im_subcategory_id' => $subcategory_id,
                'proc_im_category_id' => $category_id,
                'selling_price' =>$input['selling_price'][$i],
                'initial_quantity' => $input['quantity'][$i],
                'current_quantity' => $input['quantity'][$i],

                'procurement_requisition_items_id' => $input['procurement_requisition_items_arr'][$i],
                'quantity_rejected_on_delivery' => $input['rejected_quantity'][$i]
            );
        }
       
        $this->db->insert_batch('procurement_delivery_challan_items', $items);

        // Update PO items status to Delivered
        $procurement_requisition_items_id_arr= $input['procurement_requisition_items_arr'];
        $PO_id= $input['PO_id'];
        $quantity_delivered= $input['quantity'];

        if(!empty($procurement_requisition_items_id_arr)) {
            foreach($procurement_requisition_items_id_arr as $key => $val) {
                $oldDelivered= $this->db->select('delivered_quantity')->where('id', $val)->get('procurement_requisition_items')->row();
                if(!empty($oldDelivered)) {
                    $update_requisition_items[]= array(
                        'id' => $val,
                        'delivered_quantity' => ($oldDelivered->delivered_quantity + $quantity_delivered[$key])
                    );
                }
            }

            if(!empty($update_requisition_items)) {
                $this->db->update_batch('procurement_requisition_items', $update_requisition_items, 'id');
            }
        }

        $requition_items= $this->db->select('id, item_quantity, ifnull(delivered_quantity, 0) as delivered_quantity')->where('procurement_requisition_id', $PO_id)->get('procurement_requisition_items')->result();
        if(!empty($requition_items)) {
            $PO_delivery_status= 'Delivered';
            $PO_completion_status= 'Completed';
            foreach($requition_items as $key => $val) {
                if($val->item_quantity == $val->delivered_quantity) {
                    $update_requisition_item_status[]= array(
                        'id' => $val->id,
                        'delivery_status' => 'Delivered'
                    );
                } else if($val->delivered_quantity == 0) {
                    $update_requisition_item_status[]= array(
                        'id' => $val->id,
                        'delivery_status' => 'Not Delivered'
                    );
                    $PO_delivery_status= 'Partially Delivered';
                    $PO_completion_status= '';
                } else if($val->delivered_quantity > 0 && $val->delivered_quantity < $val->item_quantity) {
                    $update_requisition_item_status[]= array(
                        'id' => $val->id,
                        'delivery_status' => 'Partially Delivered'
                    );
                    $PO_delivery_status= 'Partially Delivered';
                    $PO_completion_status= '';
                }
            }
            if(!empty($update_requisition_item_status)) {
                $this->db->update_batch('procurement_requisition_items', $update_requisition_item_status, 'id');
                $po_update_arr=array('delivery_status' => $PO_delivery_status);
                // if($PO_completion_status != '') { // PO completion status is set only when all items are delivered. Invoice and voucher can be created only when PO is completed, partially delivered or delivered and in invoice and voucher case PO status is not going to change.
                //     $po_update_arr['status'] = $PO_completion_status;
                // }
                $this->db->where('id', $PO_id)->update('procurement_requisition', $po_update_arr);
            }
        }

        



        $this->db->trans_complete();
        return $invoiceId;
    }

    function get_delivered_POs() { // It will return the Item, Services and Milestones based POs
        $this->db_readonly->select('id,request_number, date_format(created_on, "%d-%m-%Y") as created_on, ifnull(po_name, "-") as po_name')
        ->from('procurement_requisition')
        // ->where_in('LOWER(status)', ['approved', 'sent to vendor', 'partially delivered', 'completed']) // We can create invoice only for delivered and partially delivered because PO will be completed when all mentioned items with quantity is delivered with delivery challan. That means we should pickup only partially and fully delivered POs from 'delivery_status' column in 'procurement_requisition table.
        // ->where('is_active', 1) // 'is_active' is not required because once the items are delivered, we must have t pay the vendor and for this invoice is required.
        ->where_not_in('LOWER(delivery_status)', ['not delivered', 'not_delivered']) // 'Not Delivered' status means delivery is not completed yet so that invoice cannot be completed.(RAHUL has to implement this)
        ->order_by('id', 'desc');
        $x= $this->db_readonly->get()->result();

        return $x;
        // echo '<pre>'; print_r($this->db_readonly->last_query($x)); die();
    }

    function get_details_PO_wise() {
        $purchase_order_id= $this->input->post('purchase_order_id');
        $vendor= $this->db_readonly->select("ifnull(pvm.vendor_code, '-') as vendor_code, pr.vendor_id, ifnull(pvm.vendor_name, '-') as vendor_name")
                    ->from('procurement_requisition pr')
                    ->join('procurement_vendor_master pvm', 'pvm.id = pr.vendor_id')
                    ->where('pr.id', $purchase_order_id)
                    ->get()->row();
        $instruments= new stdClass();
        if(!empty($vendor))
        $instruments= $this->db_readonly->select("ifnull(pvpi.name, '-') as instrument_name, pvpi.id, ifnull(pvpi.payment_type, '-') as payment_type, ifnull(pvpi.bank_name, '-') as bank_name")
                    ->from('procurement_vendor_payment_instruments pvpi', 'pvpi.vendor_id = pvm.id', 'left')
                    ->where('pvpi.vendor_id', $vendor->vendor_id)
                    ->get()->row();
        return ['vendor' => $vendor, 'instruments' => $instruments];
    }

    function save_invoice_basic_details() {
        $input= $this->input->post();
        // echo '<pre>'; print_r($input); die();

        $insert_update_type= $input['insert_update_type'];
        $invoice_number= $input['invoice_number'];
        $invoice_date= $input['invoice_date'];
        $invoice_type= $input['invoice_type'];
        $due_date= $input['due_date'];
        $purchase_order_id= $input['purchase_order_id'];
        $vendor_id= $input['vendor_id'];
        $invoice_status= $input['invoice_status'];
        $invoice_remarks= $input['invoice_remarks'];
        $vendor_instruments_id= isset($input['vendor_instruments_id']) && $input['vendor_instruments_id'] != '' ? $input['vendor_instruments_id'] : NULL;

        if($insert_update_type == 'Add') {
            $derivedInvoiceNumber= $this->__getLatestInvoiceNumber();
        } else {
            $derivedInvoiceNumber= $invoice_number;
        }

        $insertInvoiceMaster= array(
            'invoice_number' => $derivedInvoiceNumber,
            'invoice_date' => date('Y-m-d', strtotime($invoice_date)),
            'invoice_type' => $invoice_type,
            'purchase_order_id' => $purchase_order_id,
            'vendor_payment_instruments_id' => $vendor_instruments_id,
            'due_date' => date('Y-m-d', strtotime($due_date)),
            'invoice_status' => $invoice_status,
            'invoice_remarks' => $invoice_remarks,
            'created_by' => $this->authorization->getAvatarStakeHolderId(),
            'last_modified_by' => $this->authorization->getAvatarStakeHolderId()
        );

        

        $this->db->trans_start();
            $invoice_master_id= $input['invoice_master_id'];
            if($insert_update_type == 'Add') {
                $this->db->insert('procurement_invoice_master', $insertInvoiceMaster);
                $invoice_master_id= $this->db->insert_id();
            } else {
                $this->db->where('id', $input['invoice_master_id'])->update('procurement_invoice_master', $insertInvoiceMaster);
            }

            $is_approvers_added= 0;
            $getAllInvoiceApprovers= $this->db->select("approver_id")->where('procurement_requisition_id', $purchase_order_id)->order_by('id')->get('procurement_po_approvals')->result();
            if(!empty($getAllInvoiceApprovers) && $insert_update_type == 'Add') {
                $is_approvers_added= 1;
                foreach($getAllInvoiceApprovers as $key => $val) {
                    $level= intval($key) + 1;
                    if($level == count($getAllInvoiceApprovers)) {
                        $level= "Financial Approver";
                    } else {
                        $level= "Level $level";
                    }
                    $insertInvoiceApprovers[]= array(
                        'invoice_id' => $invoice_master_id,
                        'approval_level' => $level,
                        'approver_id' => $val->approver_id,
                        'approval_status' => 'Pending',
                        'action_taken_on' => date('Y-m-d H:i:s')
                    );
                }
                if(!empty($insertInvoiceApprovers)) {
                    $this->db->insert_batch('procurement_invoice_approvals', $insertInvoiceApprovers);
                }
            }
        $this->db->trans_complete();

        $gdc = $this->db->select("pdcm.id as delivery_challan_master_id, sum(pdci.initial_quantity) as initial_quantity, ifnull(pdcm.bill_no, '-') as bill_no, ifnull(pdcm.delivery_note, '-') as narration, ifnull(pdcm.delivery_challan_note_number, '-') as delivery_challan_note_number, ifnull(pdcm.total_amount, 0) as total_amount")
                    ->join('procurement_delivery_challan_items pdci', 'pdci.invoice_master_id = pdcm.id')
                    ->where('pdcm.procurement_requisition_id', $purchase_order_id)
                    ->group_by('pdcm.id')
                    ->get('procurement_delivery_challan_master pdcm')->result();

        $sdc = $this->db->select("ps.id as procurement_sdc_id, sum(psd.delivered_qty) as initial_quantity, ps.sdc_number, ifnull(ps.other_remarks, '-') as narration, ifnull(ps.total_amount, 0) as total_amount")
                    ->from('procurement_sdc ps')
                    ->join('procurement_sdc_deliveries psd', 'psd.procurement_sdc_id = ps.id')
                    ->where('ps.procurement_requisition_id', $purchase_order_id)
                    ->group_by('ps.id')
                    ->get()->result();
        
        $po_details= $this->db->select("pr.id, pr.request_number, pr.delivery_status, sum(pri.item_quantity) as total_items, pr.request_type, ifnull(pr.remarks, '-') as remarks, pr.po_name, pr.status, pr.delivery_status, sum(pri.total_item_amt) as total_po_amount, sum(pri.gst_amt_total) as total_po_gst_amount")
                    ->from('procurement_requisition pr')
                    ->join('procurement_requisition_items pri', 'pri.procurement_requisition_id = pr.id')
                    ->where('pr.id', $purchase_order_id)
                    ->get()->row();

        $invoiced_details= $this->db->select("sum(subtotal_amount) as subtotal_amount, purchase_order_id, group_concat(id) as invoice_master_ids_str, sum(discount_amount) as discount_amount, sum(total_amount) as total_amount, sum(invoice_amount) as invoiced_amount")
                    ->where('purchase_order_id', $purchase_order_id)
                    ->where_not_in('LOWER(invoice_status)', ['cancelled', 'rejected', 'deleted']) // Draft should not take because in future it may active
                    ->group_by('purchase_order_id')
                    ->get('procurement_invoice_master')->row();
        if(!empty($invoiced_details)) {
            $voucher_details= $this->db->select("sum(pvd.net_amount) as net_amount, ifnull(sum(ifnull(pvd.tds_amount, 0)), 0) as tds_amount")
                        ->join('procurement_voucher_master pvm', 'pvm.id = pvd.voucher_id')
                        ->where_in('pvd.invoice_id', explode(',', $invoiced_details->invoice_master_ids_str))
                        ->where('LOWER(pvm.voucher_status)', 'approved') 
                        ->where('LOWER(pvm.payment_status)', 'paid') 
                        ->get('procurement_voucher_details pvd')->row();
            if(!empty($voucher_details)) {
                $invoiced_details->released_amount= 1*$voucher_details->net_amount + 1*$voucher_details->tds_amount;
            } else {
                $invoiced_details->released_amount= 0;
            }
        }

        $sdc_type= $this->db->select("reference_type")
                    ->join('procurement_sdc psdc', 'psdcd.procurement_sdc_id = psdc.id')
                    ->where('psdc.procurement_requisition_id', $purchase_order_id)->get('procurement_sdc_deliveries psdcd')->row();
        $reference_type= 'item';
        if(!empty($sdc_type)) {
            $reference_type= $sdc_type->reference_type;
        }

        if($this->db->trans_status())
        return array(
            'status' => 1,
            'reference_type' => $reference_type,
            'gdc' => $gdc,
            'sdc' => $sdc,
            'po_details' => $po_details,
            'invoiced_details' => $invoiced_details,
            'invoice_master_id' => $invoice_master_id,
            'vendor_id' => $vendor_id,
            'purchase_order_id' => $purchase_order_id,
            'derivedInvoiceNumber' => $derivedInvoiceNumber,
            'is_approvers_added' => $is_approvers_added
        );
        // else { // If not returned, that means something is wrong. So rollback action is taken
            $this->db->trans_rollback();
            return array(
                'status' => 0,
                'reference_type' => $reference_type,
                'invoice_master_id' => 0,
                'vendor_id' => $vendor_id,
                'purchase_order_id' => $purchase_order_id,
                'derivedInvoiceNumber' => '',
                'is_approvers_added' => $is_approvers_added
            );
        // }

    }

    function __getLatestInvoiceNumber() {
        $letestInvoice= $this->db->select("max(id) as maxId")->get('procurement_invoice_master')->row();
        $schoolShort= $this->settings->getSetting('school_short_name');
        $schoolShort= strtoupper($schoolShort);
        $currentAcadYear = $this->db->where('id', $this->yearId)->get('academic_year')->row();
        if(!empty($currentAcadYear)) {
            $currentAcadYearName = $currentAcadYear->acad_year;
        } else {
            $currentAcadYearName = '';
        }


        $maxNumber= intval($letestInvoice->maxId) + 1;
        if($letestInvoice && isset($letestInvoice) && !empty($letestInvoice)) {
            $derivedInvoiceNumber= "INV-$schoolShort-$currentAcadYearName-00$maxNumber";
        } else {
            $derivedInvoiceNumber= "INV-$schoolShort-$currentAcadYearName-001";
        }
        return $derivedInvoiceNumber;
    }

    function save_delivery_items() {
        $input= $this->input->post();
        // echo '<pre>'; print_r($input); die();
        $invoice_master_id= $input['invoice_master_id'];
        $insert_update_type= $input['insert_update_type'];
        $invoiced_amount_form= $input['invoiced_amount_form'];  

        $cgst_per= $input['cgst_per'];
        $cgst_amt= $input['cgst_amt'];
        $sgat_per= $input['sgat_per'];
        $sgat_amt= $input['sgat_amt'];
        $round_form= $input['round_form'];
        $gst_amount_form= 1 * $cgst_amt + 1 * $sgat_amt;

        $total_invoiced_amount_form= $input['total_invoiced_amount_form']; 
        $step_number= $input['step_number'];
        
        $status= false;
        $this->db->trans_start();
            // if($insert_update_type == 'Add' || $insert_update_type == 'No More Updates') {
                $update_invoice_master= array(
                    'cgst_percentage' => $cgst_per,
                    'sgst_percentage' => $sgat_per,
                    'cgst_amount' => $cgst_amt,
                    'sgst_amount' => $sgat_amt,
                    'gst_amount' => $gst_amount_form,
                    'subtotal_amount' => $total_invoiced_amount_form,
                    'discount_amount' => 0,
                    'total_amount' => $total_invoiced_amount_form,
                    'invoice_amount' => $invoiced_amount_form,
                    'rounding_adjustment' => $round_form
                );
                $status= $this->db->where('id', $invoice_master_id)->update('procurement_invoice_master', $update_invoice_master);
            // }
        $this->db->trans_complete();

        if($status) {
            $approvers = $this->db->select("
                                pia.approval_level, 
                                pia.approval_status, 
                                IF(
                                    pia.approver_id IS NOT NULL AND pia.approver_id > 0, 
                                    CONCAT(sm.first_name, ' ', IFNULL(sm.last_name, '')), 
                                    'Admin'
                                ) AS staff, 
                                IFNULL(sd.department, '-') AS department
                            ")
                        ->from('procurement_invoice_approvals pia')
                        ->join('staff_master sm', 'sm.id = pia.approver_id', 'left')
                        ->join('staff_departments sd', 'sd.id = sm.department', 'left')
                        ->where('invoice_id', $invoice_master_id)
                        ->get()->result();
            
            return array(
                'status' => 1,
                'invoice_master_id' => $invoice_master_id,
                'approvers' => $approvers
            );
        }

        return array(
            'status' => 0,
            'invoice_master_id' => $invoice_master_id,
            'approvers' => []
        );
    }

    function __save_services_delivery_into_invoice($input) {
        $invoice_master_id= $input['invoice_master_id'];
        $selected_purchase_order_id= $input['selected_purchase_order_id'];
        $selected_vendor_id= $input['selected_vendor_id'];
        $selected_reference_type= $input['selected_reference_type'];

        $procurement_sdc_id_arr= $input['procurement_sdc_id'];
        $amount_to_be_invoiced_arr=$input['amount_to_be_invoiced'];
        $discount_for_invoicing_arr=$input['discount_for_invoicing'];
        $total_after_discount_delivery_level_for_invoicing_arr=$input['total_after_discount_delivery_level_for_invoicing'];

        $price_summary_td=$input['price_summary_td'];
        $total_deliveries_summary_td=$input['total_deliveries_summary_td'];
        $discount_summary_td=$input['discount_summary_td'];
        $total_after_discount_summary_td=$input['total_after_discount_summary_td'];

        $insert_invoice_items= [];
        $update_challan_items= [];
        if(!empty($procurement_sdc_id_arr)) {
            foreach($procurement_sdc_id_arr as $key => $sdc_id) {
                $insert_invoice_items[]= array(
                    'invoice_master_id' => $invoice_master_id,
                    'delivery_challan_type' => $selected_reference_type == 'Goods' ? 'GDC' : 'SDC',
                    'delivery_challan_master_id' => $sdc_id,
                    'amount' => $amount_to_be_invoiced_arr[$key],
                    'discount_amount' => $discount_for_invoicing_arr[$key],
                    'total_amount' => $total_after_discount_delivery_level_for_invoicing_arr[$key]
                );
                $invoiced= $this->db->select("ifnull(invoiced_amount, 0) as invoiced_amount")->where('id', $sdc_id)->get('procurement_sdc')->row();
                if(!empty($invoiced))
                $update_challan_master[]= array(
                    'id' => $sdc_id,
                    'invoiced_amount' => intval($invoiced->invoiced_amount + $amount_to_be_invoiced_arr[$key])
                );
            }
        }

        $this->db->trans_start();
            if(!empty($insert_invoice_items)) {
                $this->db->insert_batch('procurement_invoice_items', $insert_invoice_items);
            }
            if(!empty($update_challan_master)) {
                $this->db->update_batch('procurement_sdc', $update_challan_master, 'id');
            }
            $update_amount_in_invoice_master= array(
                'subtotal_amount' => (1*$price_summary_td),
                'discount_amount' => 1*$discount_summary_td,
                'total_amount' => (1*$total_after_discount_summary_td),
                'invoice_status' => 'Pending'
            );
            // echo '<pre>'; print_r($update_amount_in_invoice_master); die();
            $this->db->where('id', $invoice_master_id)->update('procurement_invoice_master', $update_amount_in_invoice_master);

            // History
             $history= array(
                'invoice_id' => $invoice_master_id,
                'created_by' => $this->authorization->getAvatarStakeHolderId(),
                'event_type' => 'Invoice Initiated',
                'event_description' => "Invoice creation has been initiated And delivery challan item is added in the invoice.",
                'created_on' => date('Y-m-d H:i:s')
            );
            $this->db->insert('procurement_invoice_history', $history);
        $this->db->trans_complete();

        return $this->db->trans_status();
    }

    function __save_goods_deliveries_into_invoice($input) {
        // $insert_update_type= $input['insert_update_type'];
        $invoice_master_id= $input['invoice_master_id'];
        $selected_purchase_order_id= $input['selected_purchase_order_id'];
        $selected_vendor_id= $input['selected_vendor_id'];
        $selected_reference_type= $input['selected_reference_type'];

        $delivery_challan_master_id_arr= $input['delivery_challan_master_id'];
        $amount_to_be_invoiced_arr= $input['amount_to_be_invoiced'];
        $discount_for_invoicing_arr= $input['discount_for_invoicing'];
        $total_after_discount_delivery_level_for_invoicing_arr=$input['total_after_discount_delivery_level_for_invoicing'];

        $price_summary_td=$input['price_summary_td'];
        $total_deliveries_summary_td=$input['total_deliveries_summary_td'];
        $discount_summary_td=$input['discount_summary_td'];
        $total_after_discount_summary_td=$input['total_after_discount_summary_td'];

        $insert_invoice_items= [];
        $update_challan_items= [];
        if(!empty($delivery_challan_master_id_arr)) {
            foreach($delivery_challan_master_id_arr as $key => $delivery_challan_master_id) {
                $insert_invoice_items[]= array(
                    'invoice_master_id' => $invoice_master_id,
                    'delivery_challan_type' => $selected_reference_type == 'Goods' ? 'GDC' : 'SDC',
                    'delivery_challan_master_id' => $delivery_challan_master_id,
                    'amount' => $amount_to_be_invoiced_arr[$key],
                    'discount_amount' => $discount_for_invoicing_arr[$key],
                    'total_amount' => $total_after_discount_delivery_level_for_invoicing_arr[$key]
                );
                $invoiced= $this->db->select("ifnull(invoiced_amount, 0) as invoiced_amount")->where('id', $delivery_challan_master_id)->get('procurement_delivery_challan_master')->row();
                if(!empty($invoiced))
                $update_challan_masters[]= array(
                    'id' => $delivery_challan_master_id,
                    'invoiced_amount' => intval($invoiced->invoiced_amount + $amount_to_be_invoiced_arr[$key])
                );
            }
        }

        $this->db->trans_start();
            if(!empty($insert_invoice_items)) {
                $this->db->insert_batch('procurement_invoice_items', $insert_invoice_items);
            }
            if(!empty($update_challan_masters)) {
                $this->db->update_batch('procurement_delivery_challan_master', $update_challan_masters, 'id');
            }
            $update_amount_in_invoice_master= array(
                'subtotal_amount' => (1*$price_summary_td),
                'discount_amount' => 1*$discount_summary_td,
                'total_amount' => (1*$total_after_discount_summary_td),
                'invoice_status' => 'Pending'
            );
            // echo '<pre>'; print_r($update_amount_in_invoice_master); die();
            $this->db->where('id', $invoice_master_id)->update('procurement_invoice_master', $update_amount_in_invoice_master);

            // History
             $history= array(
                'invoice_id' => $invoice_master_id,
                'created_by' => $this->authorization->getAvatarStakeHolderId(),
                'event_type' => 'Invoice Initiated',
                'event_description' => "Invoice creation has been initiated And delivery challan item is added in the invoice.",
                'created_on' => date('Y-m-d H:i:s')
            );
            $this->db->insert('procurement_invoice_history', $history);
        $this->db->trans_complete();

        return $this->db->trans_status();
    }
    
    function add_document() {
        $file= $_FILES;
        // echo '<pre>'; print_r($_POST); die();
        $invoice_master_id= $this->input->post('invoice_master_id');
        $additional_description_notes= $this->input->post('additional_description_notes');
        if($file) {
            // $this->load->library('filemanager');
            // $additional_attachements = $this->s3FileUpload($_FILES['additional_attachements'],'invoice_documents');
            
            $insert_docs= array(
                'invoice_id' => $invoice_master_id,
                'file_name' => $this->input->post('fileName'),
                'document_type' => $this->input->post('invoice_document_type'),
                'file_type' => $this->input->post('fileExtentionType'),
                'file_size' => $this->input->post('fileSizeBytes'),
                'document_description' => $additional_description_notes,
                'uploaded_by' => $this->authorization->getAvatarStakeHolderId(),
                'file_path' => ($this->input->post('path')) ? $this->input->post('path') : NULL
            );
            $this->db->insert('procurement_invoice_documents', $insert_docs);
            $invoice_attachments_id= $this->db->insert_id();
             // History
             $file_name= $this->input->post('fileName');
             $history= array(
                'invoice_id' => $invoice_master_id,
                'created_by' => $this->authorization->getAvatarStakeHolderId(),
                'event_type' => 'Invoice Document Added',
                'event_description' => "Invoice document has been added with the name $file_name. $additional_description_notes",
                'created_on' => date('Y-m-d H:i:s')
            );
            $this->db->insert('procurement_invoice_history', $history);

           
            $this->load->library('filemanager');
            $url= $this->filemanager->getFilePath($this->input->post('path'));

            // echo '<pre>'; print_r($url); die();

            $status= array(
                'status' => 1,
                'invoice_master_id' => $invoice_master_id,
                'invoice_attachments_id' => $invoice_attachments_id,
                'absolute_path' => $url
            );
        } else {
            $status= array(
                'status' => 0,
                'invoice_master_id' => $invoice_master_id
            );
        }
        return $status;
    }

    public function s3FileUpload($file,$folder_name='Other_Documents') {
        if($file['tmp_name'] == '' || $file['name'] == '') {
          return ['status' => 'empty', 'file_name' => ''];
        }        
        return $this->filemanager->uploadFile($file['tmp_name'],$file['name'],$folder_name);
    }

    function getDocumentURL($invoice_attachements_id) {
        $x= $this->db_readonly->select("file_path")->where('id', $invoice_attachements_id)->get('procurement_invoice_documents')->row();
        // echo '<pre>'; print_r($this->db_readonly->last_query($x)); die();
        return $x;
    }

    function remove_document() {
        $invoice_attachment_id= $this->input->post('invoice_attachment_id');
        return $this->db->where('id', $invoice_attachment_id)->delete('procurement_invoice_documents');
    }

    function submit_invoice() {
        $input= $this->input->post();
        $invoice_master_id= $input['invoice_master_id'];
        $this->db->trans_start();
            $this->db->where('id', $invoice_master_id)->update('procurement_invoice_master', array('invoice_status' => 'Pending'));
        
            // History
             $file_name= $this->input->post('fileName');
             $history= array(
                'invoice_id' => $invoice_master_id,
                'created_by' => $this->authorization->getAvatarStakeHolderId(),
                'event_type' => 'Invoice Created & Sent for Payment Approvals',
                'event_description' => "Invoice has been created and filled all the required invoice information and sent for payment approvals",
                'created_on' => date('Y-m-d H:i:s')
            );
            $this->db->insert('procurement_invoice_history', $history);
        $this->db->trans_complete();
        return array(
            'status' => 1,
            'invoice_master_id' => $invoice_master_id
        );
    }
    function get_invoice_list($staff_filter, $invoice_status) {
        $logged_in_user_id= $this->authorization->getAvatarStakeHolderId();

        $this->db_readonly->select("pim.id as invoice_master_id, ifnull(pr.request_number, '-') as PO_request_number, pim.invoice_number, date_format(pim.invoice_date, '%d-%m-%Y') as invoice_date, pim.invoice_type, pim.purchase_order_id, ifnull(pim.subtotal_amount, 0) as subtotal_amount, ifnull(pim.discount_amount, 0) as discount_amount, ifnull(pim.total_amount, 0) as total_amount, pim.vendor_payment_instruments_id, date_format(pim.due_date, '%d-%m-%Y') as due_date, pim.payment_status, pim.invoice_status, pim.cancelled_by, date_format(pim.cancelled_on, '%d-%m-%Y') as cancelled_on, ifnull(pim.cancellation_remarks, '-') as cancellation_remarks, ifnull(pim.invoice_remarks, '-') as invoice_remarks, if(pim.created_by is not null AND pim.created_by > 0, concat(sm.first_name, ' ', ifnull(sm.last_name, '')), 'Admin') as created_by, if(pia.id is not null, group_concat(pia.approver_id), '0') as has_approved_me, ifnull(pvm.vendor_code, '-') as vendor_code, pvm.vendor_name")
            ->from('procurement_invoice_master pim')
            ->join('procurement_requisition pr', 'pr.id = pim.purchase_order_id')
            ->join('procurement_vendor_master pvm', 'pvm.id = pr.vendor_id')
            ->join('staff_master sm', 'sm.id = pim.created_by', 'left')
            ->join('procurement_invoice_approvals pia', 'pia.invoice_id = pim.id', 'left');

        if($staff_filter == 'created_by_me') {
            $this->db_readonly->where('pim.created_by', $logged_in_user_id);
        }
        if($invoice_status != 'all') {
            $this->db_readonly->where('pim.invoice_status', $invoice_status);
        }
        $data= $this->db_readonly->group_by('pim.id')
            ->order_by('pim.id', 'desc')
            ->get()->result();

        if(!empty($data) && $staff_filter == 'approval_by_me') { // All for super admin
            foreach($data as $key => $val) {
                $has_approved_me_id_arr= explode(',', $val->has_approved_me);
                if(in_array($logged_in_user_id, $has_approved_me_id_arr)) {
                    $data[$key]->has_approved_by_me= 1;
                } else {
                    unset($data[$key]);
                }
            }
        }

        // echo '<pre>'; print_r($data); die();

        return $data;
    }

    function get_unique_invoice_details($invoice_master_id) {
        $this->db_readonly->select("pim.id as invoice_master_id, ifnull(pr.request_number, '-') as PO_request_number, pim.invoice_number, date_format(pim.invoice_date, '%d-%m-%Y') as invoice_date, pim.invoice_type, pim.purchase_order_id, ifnull(pim.subtotal_amount, 0) as subtotal_amount, ifnull(pim.discount_amount, 0) as discount_amount, ifnull(pim.total_amount, 0) as total_amount, pim.vendor_payment_instruments_id, date_format(pim.due_date, '%d-%m-%Y') as due_date, pim.payment_status, pim.invoice_status, pim.cancelled_by, date_format(pim.cancelled_on, '%d-%m-%Y') as cancelled_on, ifnull(pim.cancellation_remarks, '-') as cancellation_remarks, ifnull(pim.invoice_remarks, '-') as invoice_remarks, if(pim.created_by is not null AND pim.created_by > 0, concat(sm.first_name, ' ', ifnull(sm.last_name, '')), 'Admin') as created_by, if(pia.id is not null AND pia.approval_status = 'Approved', group_concat(pia.approver_id), '0') as has_approved_me, ifnull(pvm.vendor_code, '-') as vendor_code, pvm.vendor_name, date_format(pim.created_on, '%d-%m-%Y') as created_on, pr.id as purchase_order_id")
            ->from('procurement_invoice_master pim')
            ->join('procurement_requisition pr', 'pr.id = pim.purchase_order_id')
            ->join('procurement_vendor_master pvm', 'pvm.id = pr.vendor_id')
            ->join('staff_master sm', 'sm.id = pim.created_by', 'left')
            ->join('procurement_invoice_approvals pia', 'pia.invoice_id = pim.id', 'left');
        $data= $this->db_readonly->where('pim.id', $invoice_master_id)->group_by('pim.id')
            ->get()->row();
        return $data;
    }

    // function get_delivery_invoiced_item_details($invoice_master_id) {
       
    //     $challan_type= $this->db_readonly->select("delivery_challan_type")->where('invoice_master_id', $invoice_master_id)->get('procurement_invoice_items')->row();
    //     if(!empty($challan_type)) {
    //         $challan_type= $challan_type->delivery_challan_type;
    //     } else {
    //         $challan_type= 'GDC';
    //     }

    //     $type= 'Goods';

    //     if($challan_type == 'GDC') {
    //         $type= 'Goods';
    //     } else {
    //         $service_type= $this->db_readonly->select("psd.reference_type")
    //         ->join('procurement_sdc_deliveries psd', 'psd.id = pii.delivery_challan_item_id')
    //         ->where('pii.invoice_master_id', $invoice_master_id)
    //         ->get('procurement_invoice_items pii')->row();

    //         if(!empty($service_type) && $service_type->reference_type == 'item') {
    //             $type= 'Services';
    //         } else {
    //             $type= 'Milestones';
    //         }

    //     }

    //     return $type;
    // }

    function get_invoice_attachments($invoice_master_id) {
        $this->db_readonly->select("id as invoice_attachments_id, invoice_id, ifnull(file_path, '-') as file_path, file_name, document_type, file_type, file_size, ifnull(document_description, '-') as document_description, date_format(uploaded_on, '%d-%m-%Y') as uploaded_on")
                ->from('procurement_invoice_documents')
                ->where('invoice_id', $invoice_master_id);
        $data= $this->db_readonly->get()->result();
        
        if (!empty($data)) {
            $this->load->library('filemanager');
            foreach ($data as $key => $val) {
                if ($val->file_path != '-') {
                    $url = $this->filemanager->getFilePath($val->file_path);
                    $val->url = $url;
                }
            }
        }

        return $data;
    }

    function get_invoice_approvers($invoice_master_id) {
        return $this->db_readonly->select("pia.id as invoice_approval_flow_id, pia.approver_id, ifnull(pia.approval_status, 'Pending') as status,ifnull(pia.approval_level, 'Financial Approver') as approval_level, ifnull(pia.approval_comments, '-') as comments, ifnull(sd.department, '-') as department, ifnull(sdg.designation, '-') as designation, if(TRIM(concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, ''))) != '', concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')), 'Admin') as approver")
            ->from('procurement_invoice_approvals pia')
            ->join('staff_master sm', 'sm.id = pia.approver_id', 'left')
            ->join('staff_departments sd', 'sd.id = sm.department', 'left')
            ->join('staff_designations sdg', 'sdg.id = sm.designation', 'left')
            ->where('pia.invoice_id', $invoice_master_id)
            ->get()->result();
    }

    function get_invoice_history($invoice_master_id) {
        return $this->db_readonly->select("pih.id as invoice_ledger_id, pih.invoice_id, pih.event_type, ifnull(pih.event_description, '-') as event_description, ifnull(pih.created_by, '-') as created_by, date_format(pih.created_on, '%d-%m-%Y') as created_on, if(TRIM(concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, ''))) != '', concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')), 'Admin') as created_by_name")
            ->from('procurement_invoice_history pih')
            ->join('staff_master sm', 'sm.id = pih.created_by', 'left')
            ->where('pih.invoice_id', $invoice_master_id)
            ->order_by('pih.id', 'desc')
            ->get()->result();
    }

    function cancel_invoice($invoice_master_id, $cancel_reason) {
        $this->db->trans_start();
            $this->db->where('id', $invoice_master_id)->update('procurement_invoice_master', array('invoice_status' => 'Cancelled', 'cancelled_by' => $this->authorization->getAvatarStakeHolderId(), 'cancelled_on' => date('Y-m-d H:i:s'), 'cancellation_remarks' => $cancel_reason));
        $this->db->trans_complete();
        if($this->db->trans_status()) {
            // History
            $history= array(
                'invoice_id' => $invoice_master_id,
                'created_by' => $this->authorization->getAvatarStakeHolderId(),
                'event_type' => 'Invoice Cancelled',
                'event_description' => "Invoice has been cancelled with the reason: $cancel_reason",
                'created_on' => date('Y-m-d H:i:s')
            );
            $this->db->insert('procurement_invoice_history', $history);
            return array(
                'status' => 1,
                'message' => "Invoice has been cancelled successfully"
            );
        } else {
            return array(
                'status' => 0,
                'message' => "The invoice cancellation was unsuccessful. Please try again."
            );
        }
    }

    function reject_invoice($invoice_master_id, $invoice_approval_flow_id, $approver_type, $reject_reason) {
        $this->db->trans_start();
            $this->db->where('id', $invoice_approval_flow_id)->update('procurement_invoice_approvals', array('approval_status' => 'Rejected', 'approval_comments' => $reject_reason, 'action_taken_on' => date('Y-m-d H:i:s')));
            $this->db->where('id', $invoice_master_id)->update('procurement_invoice_master', array('invoice_status' => 'Rejected'));
        $this->db->trans_complete();
        if($this->db->trans_status()) {
            // History
            $history= array(
                'invoice_id' => $invoice_master_id,
                'created_by' => $this->authorization->getAvatarStakeHolderId(),
                'event_type' => 'Invoice Rejected',
                'event_description' => "Invoice has been rejected by the $approver_type with the reason: $reject_reason",
                'created_on' => date('Y-m-d H:i:s')
            );
            $this->db->insert('procurement_invoice_history', $history);
            return array(
                'status' => 1,
                'message' => "Invoice has been rejected successfully"
            );
        } else {
            return array(
                'status' => 0,
                'message' => "The invoice rejection was unsuccessful. Please try again."
            );
        }
    }

    function get_all_comments() {
        $invoice_master_id = $this->input->post('invoice_master_id');
        $comments= $this->db_readonly->select("pia.invoice_id, pia.approval_level, pia.approver_id, ifnull(pia.approval_status, 'Pending') as approval_status, ifnull(pia.approval_comments, '-') as approval_comments, ifnull(date_format(pia.approved_on, '%d-%m-%Y'), '-') as approved_on, ifnull(date_format(pia.action_taken_on, '%d-%m-%Y'), '-') as action_taken_on, if(pia.approver_id is not null and pia.approver_id > 0, concat(sm.first_name, ' ', ifnull(sm.last_name, '')), 'Admin') as staff")
                ->from('procurement_invoice_approvals pia')
                ->join('staff_master sm', 'sm.id = pia.approver_id', 'left')
                ->where('pia.invoice_id', $invoice_master_id)
                ->order_by('pia.id', 'desc')
                ->get()->result();

                // echo '<pre>'; print_r($comments); die();

        return $comments;

    }

    function approve_invoice() {
        $invoice_master_id = $this->input->post('invoice_master_id');
        $invoice_approval_flow_id = $this->input->post('invoice_approval_flow_id');
        $approver_type = $this->input->post('approver_type');
        $approve_reason = $this->input->post('approve_reason');

        $this->db->trans_start();
            $this->db->where('id', $invoice_approval_flow_id)->update('procurement_invoice_approvals', array('approval_status' => 'Approved', 'approval_comments' => $approve_reason, 'action_taken_on' => date('Y-m-d H:i:s'), 'approved_on' => date('Y-m-d H:i:s')));
            if($approver_type == 'Financial Approver') {
                $this->db->where('id', $invoice_master_id)->update('procurement_invoice_master', array('invoice_status' => 'Approved'));
            }
        $this->db->trans_complete();
        if($this->db->trans_status()) {
            // History
            $history= array(
                'invoice_id' => $invoice_master_id,
                'created_by' => $this->authorization->getAvatarStakeHolderId(),
                'event_type' => 'Invoice Approved',
                'event_description' => "Invoice has been approved by the $approver_type with the reason: $approve_reason",
                'created_on' => date('Y-m-d H:i:s')
            );
            $this->db->insert('procurement_invoice_history', $history);
            return array(
                'status' => 1,
                'message' => "Invoice has been approved successfully"
            );
        } else {
            return array(
                'status' => 0,
                'message' => "The invoice approval process was unsuccessful. Please try again."
            );
        }
    }

    public function getInvoiceByIdV2($id) {
        $this->db_readonly->select("im.vendor_id, im.delivery_challan_note_number, im.invoice_no, im.bill_no, im.invoice_date, im.delivery_note, im.supplier_ref_no, im.order_no, im.order_date, im.mode_of_payment, im.dispatch_doc_no, im.delivery_note_date, im.total_amount, im.payment_instrument_id, im.dispatched_by, im.destination, im.created_by, im.created_on, im.last_modified_by, im.last_modified_on, im.status, im.sales_year_id, im.dc_type, im.procurement_requisition_id, vm.vendor_name,pi.vendor_id, pi.payment_type, pi.bank_name, pi.account_number, pi.ifsc_code, pi.branch, pi.cheque_in_favor_of, pi.created_on, pi.created_by, pi.last_modified_by, pi.modified_on, pi.name, date_format(im.delivery_note_date, '%d-%m-%Y') as delivery_note_date_1, date_format(im.invoice_date, '%d-%m-%Y') as invoice_date_1, date_format(im.order_date, '%d-%m-%Y') as order_date_1, im.id as invoice_master_id, ifnull(pr.request_number, '-') as request_number, if(pr.request_type is not null AND TRIM(pr.request_type) != '', pr.request_type, if(pr.id is not null, 'Consumables', '-')) as request_type, sum(pri.total_item_amt_with_gst) as total_item_amt_with_gst, ifnull(po_name, '-') as po_name");
        $this->db_readonly->from('procurement_delivery_challan_master im');
        $this->db_readonly->join('procurement_requisition pr', 'im.procurement_requisition_id=pr.id', 'left');
        $this->db_readonly->join('procurement_requisition_items pri', 'pri.procurement_requisition_id=pr.id', 'left');
        $this->db_readonly->join('procurement_vendor_master vm', 'im.vendor_id=vm.id');
        $this->db_readonly->join('procurement_vendor_payment_instruments pi', 'im.payment_instrument_id=pi.id', 'left');
        $this->db_readonly->where('im.id', $id);
        $data= $this->db_readonly->group_by('im.id')->get()->row();
        if(!empty($data)) {
            return $data;
        }
        return array();
    }

    function get_delivery_challan_details($invoice_master_id) {
        $purchase_order= $this->db_readonly->select("purchase_order_id")->where('id', $invoice_master_id)->get('procurement_invoice_master')->row();
        if(empty($purchase_order)) {
            return [];
        }
        $this->db_readonly->select("pdcm.id as delivery_challan_master_id, ifnull(pdcm.delivery_challan_note_number, 'Number Not Provided') AS delivery_challan_note_number, ifnull(pdcm.bill_no, '-') as bill_no, ifnull(pdcm.delivery_note, '-') as delivery_note, ifnull(date_format(pdcm.order_no, '%d-%m-%Y'), '-') as order_no, date_format(pdcm.created_on, '%d-%m-%Y') as created_on, pdcm.dc_type, date_format(pdcm.order_date, '%d-%m-%Y') as order_date, ifnull(pvm.vendor_code, '-') as vendor_code, pvm.vendor_name, pvm.contact_first_name, ifnull(pvm.contact_number, '-') as supplier_contact_number");
        $this->db_readonly->from('procurement_delivery_challan_master pdcm');
        $this->db_readonly->join('procurement_vendor_master pvm', 'pvm.id=pdcm.vendor_id');
        $this->db_readonly->where('pdcm.procurement_requisition_id', $purchase_order->purchase_order_id);
        return $this->db_readonly->group_by('pdcm.id')->get()->result();
    }

    function get_delivery_challan_details_SDC($invoice_master_id) {
        $purchase_order= $this->db_readonly->select("purchase_order_id")->where('id', $invoice_master_id)->get('procurement_invoice_master')->row();
        if(empty($purchase_order)) {
            return [];
        }
        $this->db_readonly->select("pdcm.id, pdcm.sdc_number, pdcm.procurement_requisition_id, ifnull(pdcm.quality_remarks, '-') as quality_remarks, ifnull(pdcm.other_remarks, '-') as other_remarks, pdcm.delivery_type, pdcm.created_by, date_format(pdcm.created_at, '%d-%m-%Y') as created_on, pdcm.updated_at, pdcm.total_amount");
        $this->db_readonly->from('procurement_sdc pdcm');
        $this->db_readonly->where('pdcm.procurement_requisition_id', $purchase_order->purchase_order_id);
        return $this->db_readonly->group_by('pdcm.id')->get()->result();
    }

    function check_if_approvers_exists() {
        $input= $this->input->post();
        $invoice_master_id= $input['invoice_master_id'];

        $is_exist= $this->db_readonly->select()
            ->from('procurement_invoice_approvals')
            ->where('invoice_id', $invoice_master_id)
            ->get()->row();
        if(!empty($is_exist)) {
            return array(
                'status' => 1,
                'message' => "Approvers already exists for this invoice"
            );
        } else {
            return array(
                'status' => 0,
                'message' => "No approvers exists for this invoice"
            );
        }
    }

    function filled_invoice_basic_details($invoice_master_id) {
        
        return $this->db_readonly->select("id, associated_voucher_amounts, delivery_challan_note_number, invoice_number, invoice_date, invoice_type, reference_invoice_id, purchase_order_id, ifnull(subtotal_amount, 0) as subtotal_amount, ifnull(discount_amount, 0) as discount_amount, ifnull(total_amount, 0) as total_amount, vendor_payment_instruments_id, due_date, payment_status, invoice_status, cancelled_by, cancelled_on, cancellation_remarks, invoice_remarks, created_by, created_on, last_modified_by, last_modified_on, voucher_created_flag, ifnull(invoice_amount, 0) as invoice_amount, ifnull(gst_amount, 0) as gst_amount, ifnull(cgst_percentage, 0) as cgst_percentage, ifnull(sgst_percentage, 0) as sgst_percentage, ifnull(cgst_amount, 0) as cgst_amount, ifnull(sgst_amount, 0) as sgst_amount, ifnull(rounding_adjustment, 0) as rounding_adjustment")
            ->where('id', $invoice_master_id)
            ->get('procurement_invoice_master')->row();
    }

    function uploaded_invoice_documents($invoice_master_id) {
        
        $documents= $this->db_readonly->select("id, invoice_id, file_name, document_type, ifnull(file_path, '-') as file_path, file_size, file_type, ifnull(document_description, '-') as document_description, date_format(uploaded_on, '%d-%m-%Y') as uploaded_on")
            ->where('invoice_id', $invoice_master_id)
            ->get('procurement_invoice_documents')->result();
        if(!empty($documents)) {
            $this->load->library('filemanager');
            foreach ($documents as $key => $val) {
                if ($val->file_path != '-') {
                    $url = $this->filemanager->getFilePath($val->file_path);
                    $val->url = $url;
                }
            }
        }
        return $documents;
    }

    function invoice_approvers($invoice_master_id) {
        
        return $this->db_readonly->select("pia.id as invoice_approval_flow_id, pia.approver_id, ifnull(pia.approval_status, 'Pending') as status,ifnull(pia.approval_level, 'Financial Approver') as approval_level, ifnull(pia.approval_comments, '-') as comments, ifnull(sd.department, '-') as department, ifnull(sdg.designation, '-') as designation, if(TRIM(concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, ''))) != '', concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')), 'Admin') as approver")
            ->from('procurement_invoice_approvals pia')
            ->join('staff_master sm', 'sm.id = pia.approver_id', 'left')
            ->join('staff_departments sd', 'sd.id = sm.department', 'left')
            ->join('staff_designations sdg', 'sdg.id = sm.designation', 'left')
            ->where('pia.invoice_id', $invoice_master_id)
            ->get()->result();
    }

    function get_po_details_via_invoice_id($invoice_id) {
        $po_details= $this->db_readonly->select("pr.id, pr.request_number, pr.delivery_status, sum(pri.item_quantity) as total_items, pr.request_type, ifnull(pr.remarks, '-') as remarks, ifnull(pr.po_name, '-') as po_name, pr.status, pr.delivery_status, sum(pri.total_item_amt) as total_po_amount, sum(pri.gst_amt_total) as total_po_gst_amount")
                    ->from('procurement_invoice_master pim')
                    ->join('procurement_requisition pr', 'pr.id = pim.purchase_order_id')
                    ->join('procurement_requisition_items pri', 'pri.procurement_requisition_id = pr.id')
                    ->where('pim.id', $invoice_id)
                    ->group_by('pr.id')
                    ->get()->row();

        return $po_details;
    }

}