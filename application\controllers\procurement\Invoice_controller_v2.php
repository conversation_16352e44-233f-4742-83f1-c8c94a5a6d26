<?php

class Invoice_controller_v2 extends CI_Controller {

    function __construct(){
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('PROCUREMENT_INVENTORY') && !$this->authorization->isModuleEnabled('PROCUREMENT_INVENTORY.MODULE')) {
            $this->session->set_flashdata('flashError', 'Module access denied. No privilege to access this module.');
            redirect('procurement/requisition_controller_v2');
        }
        $this->load->model('procurement/Vendor_model_v2');
        $this->load->model('procurement/invoice_model_v2');
    }

    public function index() {
        $data['main_content'] = 'procurement/invoice_view_v2/index';
        $this->load->view('inc/template', $data);
    }

    public function invoice_list($salesYear_id = 0) {
        $data['salesYear_id']= $salesYear_id;
        $data['invoiceData'] = $this->invoice_model_v2->get_invoice_details($salesYear_id);
        $data['salesYear'] = $this->invoice_model_v2->get_sales_year();
        $data['main_content'] = 'procurement/invoice_view_v2/invoice_list';
        $this->load->view('inc/template', $data);
    }

    public function new_invoice(){
        $data['vendorData'] = $this->Vendor_model_v2->get_vendor_details();
        $data['sales_year'] = $this->invoice_model_v2->get_sales_year();
        $data['main_content'] = 'procurement/invoice_view_v2/add_invoice';
        $this->load->view('inc/template', $data);
    }

    public function getVendorData(){
        $vendorId = $_POST['vendorId'];
        $payments = $this->Vendor_model_v2->getPaymentInsData($vendorId);
        $products = $this->invoice_model_v2->getVendorProducts($vendorId);
        echo json_encode(array('payments' => $payments, 'products' => $products));
    }

    public function getVariants(){
        $products = $_POST['products'];
        $variants = $this->Vendor_model_v2->getVariants();
        echo json_encode($variants);
    }

    public function submitInvoice(){
        $input = $this->input->post();
        $status = $this->invoice_model_v2->addInvoice();
        if($status)
            $this->session->set_flashdata('flashSuccess', 'Added Successfully.');
        else
            $this->session->set_flashdata('flashError', 'Something went wrong!');
        redirect('procurement/invoice_controller_v2');
    }

    public function readInvoice($id) {
        $data['invoiceData'] = $this->invoice_model_v2->getInvoiceById($id);
        $data['items'] = $this->invoice_model_v2->getItemsId($id);
        // echo '<pre>'; print_r($data['invoiceData']); die();
        $data['active_sales_year'] = $this->invoice_model_v2->get_active_sales_year();
        $data['main_content'] = 'procurement/invoice_view_v2/read_invoice';
        $this->load->view('inc/template', $data);
    }

    public function invoice_report($salesYear_id = 0) {
        $data['salesYear_id']= $salesYear_id;
        $invoices = $this->invoice_model_v2->getInvoiceData($salesYear_id);
        $data['salesYear'] = $this->invoice_model_v2->get_sales_year();
        $data['invoices'] = array();
        foreach ($invoices as $key => $invoice) {
            $id = $invoice->invoice_id;
            if(!array_key_exists($id, $data['invoices'])) {
                $data['invoices'][$id] = array();
                $data['invoices'][$id]['invoice_no'] = $invoice->invoice_no;
                $data['invoices'][$id]['invoiceTotal'] = $invoice->invoiceTotal;
                $data['invoices'][$id]['vendor_name'] = $invoice->vendor_name;
                $data['invoices'][$id]['invoiceDate'] = $invoice->invoiceDate;
                $data['invoices'][$id]['items'] = array();
                $data['invoices'][$id]['total'] = 0;
                $data['invoices'][$id]['created_on'] = date('d-m-Y',strtotime($invoice->created_on));
                $data['invoices'][$id]['created_by'] = $invoice->friendly_name;
            }
            $data['invoices'][$id]['items'][] = array('name' => $invoice->variantName, 'quantity' => $invoice->quantity, 'price' => $invoice->itemTotal);
            $data['invoices'][$id]['total'] += $invoice->itemTotal;
        }
        $data['main_content'] = 'procurement/invoice_view_v2/invoice_report';
        $this->load->view('inc/template', $data);
    }

    public function delete_invoice(){
        $result = $this->invoice_model_v2->delete_invoice();
        if($result)
            $this->session->set_flashdata('flashSuccess', 'Deleted Successfully.');
        else
            $this->session->set_flashdata('flashError', 'Something went wrong!');
        echo $result;
    }

    public function onclick_replace_qty_OR_price() {
        $http_response_header = $this->invoice_model_v2->onclick_replace_qty_OR_price();
        echo json_encode($http_response_header);
    }

    public function submit_vendor_return() {
        $data = $this->invoice_model_v2->submit_vendor_return();
        echo json_encode($data);
    }

    function invoice_return_report() {
        $data['salesYear'] = $this->invoice_model_v2->get_sales_year();
        $data['main_content'] = 'procurement/invoice_view_v2/invoice_return_report';
        $this->load->view('inc/template', $data);
    }

    public function get_vendor_return_hisiory() {
        $data = $this->invoice_model_v2->get_vendor_return_hisiory();
        echo json_encode($data);
    }

    public function itemWiseVendorReturns() {
        $data['salesYear'] = $this->invoice_model_v2->get_sales_year();
        $data['itemList'] = $this->invoice_model_v2->getItemList();
        $data['main_content'] = 'procurement/invoice_view_v2/itemWiseVendorReturns';
        $this->load->view('inc/template', $data);
    }

    public function getItemWiseSalesYearWiseVendors() {
        $data = $this->invoice_model_v2->getItemWiseSalesYearWiseVendors();
        echo json_encode($data);
    }

    public function submit_vendor_return_item_wise() {
        $data = $this->invoice_model_v2->submit_vendor_return_item_wise();
        echo json_encode($data);
    }

    public function index_v2() {
         if (!$this->authorization->isAuthorized('PROCUREMENT_DELIVERY_V2.MODULE')) {
            $this->session->set_flashdata('flashError', 'Module access denied. No privilege to access this module.');
            redirect('procurement/requisition_controller_v2');
        }
        $data['main_content'] = 'procurement/invoice_view_v2/delivery_dashboard';
        $this->load->view('inc/template', $data);
    }

    public function add_goods_delivery_challan(){
        $data['POs'] = $this->invoice_model_v2->get_active_POs();
        $data['vendorData'] = $this->Vendor_model_v2->get_vendor_details();
        $data['sales_year'] = $this->invoice_model_v2->get_sales_year();
        $data['main_content'] = 'procurement/invoice_view_v2/add_goods_delivery_challan';
        $this->load->view('inc/template', $data);
    }

    function get_po_data() {
        $PO_id= $this->input->post('PO_id');
        $PO_data = $this->invoice_model_v2->get_po_data($PO_id);
        echo json_encode($PO_data);
    }

    public function submitInvoicePO(){
        $status = $this->invoice_model_v2->addInvoicePO();
        if($status)
            $this->session->set_flashdata('flashSuccess', 'Added Successfully.');
        else
            $this->session->set_flashdata('flashError', 'Something went wrong!');
        redirect('procurement/invoice_controller_v2/readInvoiceV2/'.$status);
    }

    public function invoice_management_dashboard(){
        $data['main_content'] = 'procurement/invoice_view_v2/invoice_management_dashboard';
        $this->load->view('inc/template', $data);
    }

    public function manage_all_invoices(){
        if (!$this->authorization->isAuthorized('PROCUREMENT_INVOICE.VIEW_DETAILS')) {
            $this->session->set_flashdata('flashError', 'Module access denied. No privilege to access this module.');
            redirect('procurement/requisition_controller_v2');
        }
        $data['main_content'] = 'procurement/invoice_view_v2/manage_all_invoices';
        $this->load->view('inc/template', $data);
    }

    function invoice_life_cycle_report() {
        if (!$this->authorization->isAuthorized('PROCUREMENT_INVOICE.VIEW_DETAILS')) {
            $this->session->set_flashdata('flashError', 'Module access denied. No privilege to access this module.');
            redirect('procurement/requisition_controller_v2');
        }
        $data['main_content'] = 'procurement/invoice_view_v2/invoice_life_cycle_report';
        $this->load->view('inc/template', $data);
    }

    public function add_new_invoice_v2(){
        $data['POs'] = $this->invoice_model_v2->get_delivered_POs();
        $data['sales_year'] = $this->invoice_model_v2->get_sales_year();
        $max_file_size=  $this->settings->getSetting('resources');
        $data['MAX_FILE_SIZE'] = '5MB';
        if(isset($max_file_size) && !empty($max_file_size)) {
            $data['MAX_FILE_SIZE']= $max_file_size->resource_size;
        }
        // echo '<pre>'; print_r($max_file_size); die();
        $data['main_content'] = 'procurement/invoice_view_v2/add_new_invoice_v2';
        $this->load->view('inc/template', $data);
    }

    function get_details_PO_wise() {
        $PO_data = $this->invoice_model_v2->get_details_PO_wise();
        echo json_encode($PO_data);
    }

    function save_invoice_basic_details() {
        $returned = $this->invoice_model_v2->save_invoice_basic_details();
        // echo '<pre>';print_r($returned); die();
        echo json_encode($returned);
    }

    function save_delivery_items() {
        $returned = $this->invoice_model_v2->save_delivery_items();
        // echo '<pre>';print_r($returned); die();
        echo json_encode($returned);
    }

    function add_document() {
        $data= $this->invoice_model_v2->add_document();
        echo json_encode($data);
    }

    function download_invoice_attachement($invoice_attachements_id= 0) {
        $this->load->library('filemanager');
        if($invoice_attachements_id == 0) {
            $this->session->set_flashdata('flashError', 'Something Went Wrong OR Attachement not found');
            redirect('procurement/invoice_controller_v2/manage_all_invoices');
        }

        $file_link = $this->invoice_model_v2->getDocumentURL($invoice_attachements_id);
    //    echo '<pre>'; print_r($file_link); die();
        $signed_resource = $this->filemanager->getSignedUrlWithExpiry($file_link->file_path, '+5 minutes');
        $file = explode("/", $file_link->file_path);
        $file_name = 'invoice_attachment_'.$invoice_attachements_id;
        $fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
        $data = file_get_contents($signed_resource);
        $this->load->helper('download');
        force_download($fname, $data, TRUE);
        $this->load->library('user_agent');
        redirect($this->agent->referrer());	
    }

    function remove_document() {
        $data= $this->invoice_model_v2->remove_document();
        echo json_encode($data);
    }

    function submit_invoice() {
        $data= $this->invoice_model_v2->submit_invoice();
        echo json_encode($data);
    }

    function get_invoice_list() {
        $staff_filter = $this->input->post('staff_filter');
        $invoice_status = $this->input->post('invoice_status');
        $invoices = $this->invoice_model_v2->get_invoice_list($staff_filter, $invoice_status);
        // echo '<pre>'; print_r($invoices); die();
        echo json_encode($invoices);
    }

    public function view_invoice($invoice_master_id) {

        $invoice['invoiceBasicDetails']= $this->invoice_model_v2->get_unique_invoice_details($invoice_master_id);

        // $invoice['type']= $this->invoice_model_v2->get_delivery_invoiced_item_details($invoice_master_id);

        $invoice['deliveryChallansDetails']= $this->invoice_model_v2->get_delivery_challan_details($invoice_master_id);
        $invoice['deliveryChallansDetailsSDC']= $this->invoice_model_v2->get_delivery_challan_details_SDC($invoice_master_id);
        $invoice['invoiceAttachements']= $this->invoice_model_v2->get_invoice_attachments($invoice_master_id);
        $invoice['invoiceApprovers']= $this->invoice_model_v2->get_invoice_approvers($invoice_master_id);
        $invoice['invoiceHistory']= $this->invoice_model_v2->get_invoice_history($invoice_master_id);

        $invoice['loggedInId']= $this->authorization->getAvatarStakeholderId();

        $invoice['invoiceCreateEdit']= $this->authorization->isAuthorized('PROCUREMENT_INVOICE.CREATE_OR_EDIT');
        $invoice['invoiceApprove']= $this->authorization->isAuthorized('PROCUREMENT_INVOICE.APPROVE');
        
        $invoice['invoice_master_id']= $invoice_master_id;

        // echo '<pre>'; print_r($invoice); die();

        $invoice['main_content'] = 'procurement/invoice_view_v2/view_invoice';
        $this->load->view('inc/template', $invoice);
    }

    function cancel_invoice() {
        $invoice_master_id = $this->input->post('invoice_master_id');
        $cancel_reason = $this->input->post('cancel_reason');
        $data= $this->invoice_model_v2->cancel_invoice($invoice_master_id, $cancel_reason);
        echo json_encode($data);
    }

    function reject_invoice() {
        $invoice_master_id = $this->input->post('invoice_master_id');
        $invoice_approval_flow_id = $this->input->post('invoice_approval_flow_id');
        $approver_type = $this->input->post('approver_type');
        $reject_reason = $this->input->post('reject_reason');
        $data= $this->invoice_model_v2->reject_invoice($invoice_master_id, $invoice_approval_flow_id, $approver_type, $reject_reason);
        echo json_encode($data);
    }

    function get_all_comments() {
        $data= $this->invoice_model_v2->get_all_comments();
        
        echo json_encode($data);
    }

    function approve_invoice() {
        $data= $this->invoice_model_v2->approve_invoice();
        echo json_encode($data);
    }

    public function invoice_list_v2($salesYear_id = 0) {
        $data['salesYear_id']= $salesYear_id;
        $data['invoiceData'] = $this->invoice_model_v2->get_invoice_details($salesYear_id);
        $data['salesYear'] = $this->invoice_model_v2->get_sales_year();
        $data['main_content'] = 'procurement/invoice_view_v2/invoice_list_v2';
        $this->load->view('inc/template', $data);
    }

    public function readInvoiceV2($id) {
        $data['invoiceData'] = $this->invoice_model_v2->getInvoiceByIdV2($id);
        $data['items'] = $this->invoice_model_v2->getItemsId($id);
        // echo '<pre>'; print_r($data['invoiceData']); die();
        $data['active_sales_year'] = $this->invoice_model_v2->get_active_sales_year();
        $data['main_content'] = 'procurement/invoice_view_v2/read_invoice_v2';
        $this->load->view('inc/template', $data);
    }

    public function itemWiseVendorReturnsV2() {
        $data['salesYear'] = $this->invoice_model_v2->get_sales_year();
        $data['itemList'] = $this->invoice_model_v2->getItemList();
        $data['main_content'] = 'procurement/invoice_view_v2/itemWiseVendorReturnsV2';
        $this->load->view('inc/template', $data);
    }

    public function challan_v2_report($salesYear_id = 0) {
        $data['salesYear_id']= $salesYear_id;
        $invoices = $this->invoice_model_v2->getInvoiceData($salesYear_id);
        $data['salesYear'] = $this->invoice_model_v2->get_sales_year();
        $data['invoices'] = array();
        foreach ($invoices as $key => $invoice) {
            $id = $invoice->invoice_id;
            if(!array_key_exists($id, $data['invoices'])) {
                $data['invoices'][$id] = array();
                $data['invoices'][$id]['invoice_no'] = $invoice->invoice_no;
                $data['invoices'][$id]['invoiceTotal'] = $invoice->invoiceTotal;
                $data['invoices'][$id]['vendor_name'] = $invoice->vendor_name;
                $data['invoices'][$id]['invoiceDate'] = $invoice->invoiceDate;
                $data['invoices'][$id]['items'] = array();
                $data['invoices'][$id]['total'] = 0;
                $data['invoices'][$id]['created_on'] = date('d-m-Y',strtotime($invoice->created_on));
                $data['invoices'][$id]['created_by'] = $invoice->friendly_name;
            }
            $data['invoices'][$id]['items'][] = array('name' => $invoice->variantName, 'quantity' => $invoice->quantity, 'price' => $invoice->itemTotal);
            $data['invoices'][$id]['total'] += $invoice->itemTotal;
        }
        $data['main_content'] = 'procurement/invoice_view_v2/challan_v2_report';
        $this->load->view('inc/template', $data);
    }

    function goods_delivery_challan_return_report() {
        $data['salesYear'] = $this->invoice_model_v2->get_sales_year();
        $data['main_content'] = 'procurement/invoice_view_v2/goods_delivery_challan_return_report';
        $this->load->view('inc/template', $data);
    }

    function check_if_approvers_exists() {
        $data = $this->invoice_model_v2->check_if_approvers_exists();
        echo json_encode($data);
    }
    
    public function continue_add_new_invoice_v2($invoice_master_id){
        if($invoice_master_id == 0) {
            $this->session->set_flashdata('flashError', 'Something Went Wrong OR Invoice not found');
            redirect('procurement/invoice_controller_v2/manage_all_invoices');
        }
        $data['POs'] = $this->invoice_model_v2->get_delivered_POs();
        $data['sales_year'] = $this->invoice_model_v2->get_sales_year();
        $max_file_size=  $this->settings->getSetting('resources');
        $data['MAX_FILE_SIZE'] = '5MB';
        if(isset($max_file_size) && !empty($max_file_size)) {
            $data['MAX_FILE_SIZE']= $max_file_size->resource_size;
        }
        $data['invoiceBasicDetails']= $this->invoice_model_v2->filled_invoice_basic_details($invoice_master_id);
        $data['invoiceDocuments']= $this->invoice_model_v2->uploaded_invoice_documents($invoice_master_id);
        $data['invoiceApprovers']= $this->invoice_model_v2->invoice_approvers($invoice_master_id);
// echo '<pre>'; print_r($data); die();
        $data['main_content'] = 'procurement/invoice_view_v2/continue_add_new_invoice_v2';
        $this->load->view('inc/template', $data);
    }

    function get_po_details_via_invoice_id() {
        $invoice_id= $this->input->post('invoice_id');
        $PO_data = $this->invoice_model_v2->get_po_details_via_invoice_id($invoice_id);
        echo json_encode($PO_data);
    }
    
}

